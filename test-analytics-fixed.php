<?php
// اختبار قسم التحليلات المحسن
include 'config/database.php';

echo "<h1>🔍 اختبار قسم التحليلات المحسن</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ فشل الاتصال: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
}

// اختبار وظائف التحليلات الجديدة
echo "<h2>2. اختبار وظائف التحليلات الجديدة</h2>";

$analytics_functions = [
    'analytics_summary' => 'ملخص التحليلات',
    'top_articles' => 'أكثر المقالات مشاهدة',
    'content_growth' => 'نمو المحتوى',
    'categories_chart' => 'مخطط التصنيفات',
    'views_chart' => 'مخطط المشاهدات',
    'daily_views' => 'المشاهدات اليومية',
    'top_articles_table' => 'جدول أفضل المقالات',
    'categories_stats' => 'إحصائيات التصنيفات'
];

foreach ($analytics_functions as $action => $description) {
    echo "<h3>اختبار: $description</h3>";
    
    // محاكاة طلب GET
    $_GET['action'] = $action;
    
    // التقاط الإخراج
    ob_start();
    try {
        include 'php/dashboard-data.php';
        $output = ob_get_clean();
        
        // فحص الإخراج
        $data = json_decode($output, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ $description: يعمل بنجاح</p>";
            
            // عرض عينة من البيانات
            if (isset($data['chart_data'])) {
                $labels_count = count($data['chart_data']['labels'] ?? []);
                $data_count = count($data['chart_data']['data'] ?? []);
                echo "<p style='margin-right: 20px; color: #666;'>📊 البيانات: $labels_count تسمية، $data_count نقطة بيانات</p>";
            } elseif (isset($data['summary'])) {
                $summary_keys = count($data['summary']);
                echo "<p style='margin-right: 20px; color: #666;'>📋 الملخص: $summary_keys عنصر</p>";
            } elseif (isset($data['articles'])) {
                $articles_count = count($data['articles']);
                echo "<p style='margin-right: 20px; color: #666;'>📝 المقالات: $articles_count مقال</p>";
            } elseif (isset($data['categories'])) {
                $categories_count = count($data['categories']);
                echo "<p style='margin-right: 20px; color: #666;'>🏷️ التصنيفات: $categories_count تصنيف</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ $description: فشل</p>";
            if (isset($data['message'])) {
                echo "<p style='margin-right: 20px; color: red;'>خطأ: " . $data['message'] . "</p>";
            }
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ $description: خطأ في التنفيذ - " . $e->getMessage() . "</p>";
    }
    
    // تنظيف المتغيرات
    unset($_GET['action']);
}

// اختبار البيانات الفعلية
echo "<h2>3. اختبار البيانات الفعلية</h2>";

try {
    // اختبار إجمالي المشاهدات
    $result = $conn->query("SELECT SUM(views) as total_views FROM articles");
    $total_views = $result->fetch_assoc()['total_views'] ?? 0;
    echo "<p style='color: green;'>✅ إجمالي المشاهدات: " . number_format($total_views) . "</p>";
    
    // اختبار إجمالي المقالات
    $result = $conn->query("SELECT COUNT(*) as total_articles FROM articles");
    $total_articles = $result->fetch_assoc()['total_articles'] ?? 0;
    echo "<p style='color: green;'>✅ إجمالي المقالات: " . number_format($total_articles) . "</p>";
    
    // اختبار أكثر التصنيفات نشاطاً
    $result = $conn->query("
        SELECT c.name, COUNT(a.id) as count 
        FROM categories c 
        LEFT JOIN articles a ON c.id = a.category_id 
        GROUP BY c.id, c.name 
        ORDER BY count DESC 
        LIMIT 1
    ");
    
    if ($result->num_rows > 0) {
        $top_category = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ أكثر التصنيفات نشاطاً: " . $top_category['name'] . " (" . $top_category['count'] . " مقال)</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد تصنيفات</p>";
    }
    
    // اختبار المقالات الحديثة
    $result = $conn->query("
        SELECT COUNT(*) as recent_articles 
        FROM articles 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $recent_articles = $result->fetch_assoc()['recent_articles'] ?? 0;
    echo "<p style='color: green;'>✅ المقالات الحديثة (آخر 30 يوم): " . number_format($recent_articles) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار البيانات: " . $e->getMessage() . "</p>";
}

// اختبار الرسوم البيانية
echo "<h2>4. اختبار بيانات الرسوم البيانية</h2>";

try {
    // اختبار بيانات مخطط التصنيفات
    $result = $conn->query("
        SELECT c.name, COUNT(a.id) as count 
        FROM categories c 
        LEFT JOIN articles a ON c.id = a.category_id 
        GROUP BY c.id, c.name 
        HAVING count > 0 
        ORDER BY count DESC
    ");
    
    $categories_data = [];
    while ($row = $result->fetch_assoc()) {
        $categories_data[] = $row;
    }
    
    if (!empty($categories_data)) {
        echo "<p style='color: green;'>✅ بيانات مخطط التصنيفات: " . count($categories_data) . " تصنيف</p>";
        foreach ($categories_data as $category) {
            echo "<p style='margin-right: 20px; color: #666;'>• " . $category['name'] . ": " . $category['count'] . " مقال</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد بيانات لمخطط التصنيفات</p>";
    }
    
    // اختبار بيانات المشاهدات الشهرية
    $result = $conn->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            SUM(views) as total_views
        FROM articles 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    
    $views_data = [];
    while ($row = $result->fetch_assoc()) {
        $views_data[] = $row;
    }
    
    if (!empty($views_data)) {
        echo "<p style='color: green;'>✅ بيانات المشاهدات الشهرية: " . count($views_data) . " شهر</p>";
        foreach ($views_data as $month_data) {
            echo "<p style='margin-right: 20px; color: #666;'>• " . $month_data['month'] . ": " . number_format($month_data['total_views']) . " مشاهدة</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد بيانات للمشاهدات الشهرية</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار الرسوم البيانية: " . $e->getMessage() . "</p>";
}

// اختبار أداء الاستعلامات
echo "<h2>5. اختبار أداء الاستعلامات</h2>";

$performance_tests = [
    "SELECT COUNT(*) FROM articles" => "عدد المقالات",
    "SELECT COUNT(*) FROM categories" => "عدد التصنيفات",
    "SELECT SUM(views) FROM articles" => "إجمالي المشاهدات",
    "SELECT AVG(views) FROM articles WHERE views > 0" => "متوسط المشاهدات"
];

foreach ($performance_tests as $query => $description) {
    $start_time = microtime(true);
    
    try {
        $result = $conn->query($query);
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        if ($result) {
            echo "<p style='color: green;'>✅ $description: تم في {$execution_time}ms</p>";
        } else {
            echo "<p style='color: red;'>❌ $description: فشل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ $description: خطأ - " . $e->getMessage() . "</p>";
    }
}

// النتيجة النهائية
echo "<h2>6. النتيجة النهائية</h2>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🎉 تم إصلاح قسم التحليلات بنجاح!</h3>";
echo "<p><strong>المميزات الجديدة:</strong></p>";
echo "<ul>";
echo "<li>✅ ملخص التحليلات مع الإحصائيات الأساسية</li>";
echo "<li>✅ رسوم بيانية تفاعلية محسنة</li>";
echo "<li>✅ جداول تفصيلية للمقالات والتصنيفات</li>";
echo "<li>✅ مشاهدات يومية وشهرية</li>";
echo "<li>✅ إحصائيات متقدمة ومؤشرات الأداء</li>";
echo "<li>✅ واجهة مستخدم محسنة ومتجاوبة</li>";
echo "</ul>";
echo "</div>";

echo "<h2>7. روابط مفيدة</h2>";
echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 8px;'>";
echo "<p><a href='admin-dashboard.php#analytics' style='color: #d4af37; font-weight: bold;'>🎯 قسم التحليلات في لوحة التحكم</a></p>";
echo "<p><a href='fix-database.php' style='color: #0066cc; font-weight: bold;'>🔧 إصلاح قاعدة البيانات</a></p>";
echo "<p><a href='test-complete-dashboard.php' style='color: #28a745; font-weight: bold;'>🧪 اختبار شامل للوحة التحكم</a></p>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

h1 {
    color: #d4af37;
    border-bottom: 3px solid #d4af37;
    padding-bottom: 15px;
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

h2 {
    color: #2c3e50;
    margin-top: 30px;
    border-right: 4px solid #d4af37;
    padding-right: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

h3 {
    color: #34495e;
    margin-top: 20px;
    font-size: 1.1rem;
}

p {
    margin: 8px 0;
    line-height: 1.6;
}

ul {
    line-height: 1.8;
}

a {
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
