# لوحة التحكم الطبية - Dashboard

## نظرة عامة

تم إنشاء لوحة تحكم شاملة وجذابة لإدارة المدونة الطبية مع تصميم ذهبي أنيق ومرتبطة بقاعدة البيانات.

## المميزات الرئيسية

### 🎯 لوحة المعلومات الرئيسية
- عرض إحصائيات شاملة (المقالات، التصنيفات، الأطباء، الخدمات، المشاهدات)
- عرض المقالات والأطباء المضافين حديثاً
- رسوم بيانية تفاعلية لتوزيع المقالات والمشاهدات

### 📝 إدارة المقالات
- عرض جميع المقالات في جدول منظم
- إضافة مقالات جديدة
- فلترة المقالات حسب التصنيف والحالة
- البحث في المقالات
- حذف المقالات

### 🏷️ إدارة التصنيفات
- عرض التصنيفات في شكل بطاقات جذابة
- إضافة تصنيفات جديدة
- عرض عدد المقالات لكل تصنيف
- حذف التصنيفات (مع التحقق من عدم وجود مقالات مرتبطة)

### 👨‍⚕️ إدارة الأطباء
- عرض الأطباء في شكل بطاقات احترافية
- إضافة أطباء جدد مع جميع البيانات
- عرض التخصص وسنوات الخبرة
- حذف بيانات الأطباء

### 🛠️ إدارة الخدمات
- عرض الخدمات الطبية في بطاقات منظمة
- إضافة خدمات جديدة مع الأسعار والأوقات
- تصنيف الخدمات حسب النوع
- حذف الخدمات

### 📊 التحليلات والإحصائيات
- رسوم بيانية لأكثر المقالات مشاهدة
- تتبع نمو المحتوى الشهري
- إحصائيات تفاعلية ومرئية

### ⚙️ الإعدادات
- إعدادات الموقع العامة
- إعدادات المستخدم الشخصية
- تحديث البيانات والكلمات المرور

## الملفات المنشأة

### الملفات الرئيسية
- `admin-dashboard.php` - الصفحة الرئيسية للوحة التحكم
- `js/admin-dashboard.js` - ملف JavaScript للتفاعلات
- `php/dashboard-data.php` - ملف PHP لجلب البيانات
- `php/dashboard-actions.php` - ملف PHP لمعالجة العمليات

### ملفات CSS
- `style/admin-dashboard.css` - ملف التصميم (موجود مسبقاً ومحدث)

### قاعدة البيانات
- `database/den_blog.sql` - قاعدة البيانات مع البيانات التجريبية (محدثة)

## كيفية الاستخدام

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف قاعدة البيانات
SOURCE database/den_blog.sql;
```

### 2. تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`
- أو استخدم: `doctor1` / `doctor123`

### 3. الوصول للوحة التحكم
```
http://localhost/DentalManagment/admin-dashboard.php
```

## المميزات التقنية

### 🎨 التصميم
- تصميم ذهبي أنيق ومتجاوب
- استخدام خط Cairo العربي
- أيقونات Font Awesome
- تأثيرات بصرية جذابة

### 💻 التقنيات المستخدمة
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7+
- **Database**: MySQL
- **Charts**: Chart.js
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Cairo)

### 🔒 الأمان
- التحقق من تسجيل الدخول
- حماية من SQL Injection
- تشفير البيانات الحساسة
- التحقق من صحة البيانات

### 📱 التجاوب
- تصميم متجاوب مع جميع الأجهزة
- واجهة محسنة للهواتف والأجهزة اللوحية
- قوائم قابلة للطي على الشاشات الصغيرة

## الوظائف المتاحة

### ✅ المنجزة
- عرض الإحصائيات
- إضافة وحذف المقالات
- إضافة وحذف التصنيفات
- إدارة الأطباء والخدمات
- الرسوم البيانية
- البحث والفلترة
- التنبيهات التفاعلية

### 🔄 قيد التطوير
- تعديل المقالات والتصنيفات
- رفع الصور
- إدارة المستخدمين
- تصدير التقارير
- النسخ الاحتياطي

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل MySQL
   - تحقق من إعدادات `config/database.php`

2. **عدم ظهور البيانات**
   - تأكد من تشغيل ملف قاعدة البيانات
   - تحقق من وجود البيانات التجريبية

3. **مشاكل في التصميم**
   - تأكد من تحميل ملفات CSS و JavaScript
   - تحقق من الاتصال بالإنترنت لتحميل الخطوط والأيقونات

## التطوير المستقبلي

### مميزات مخططة
- نظام إشعارات متقدم
- تحليلات أكثر تفصيلاً
- إدارة الملفات والصور
- نظام النسخ الاحتياطي التلقائي
- واجهة برمجة التطبيقات (API)

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملفات السجل (logs)
- راجع إعدادات قاعدة البيانات
- تأكد من صحة مسارات الملفات

---

**ملاحظة**: هذه لوحة تحكم متكاملة وجاهزة للاستخدام مع إمكانيات توسع مستقبلية.
