<?php
// فحص شامل لمشاكل التضارب في لوحة التحكم
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>فحص مشاكل لوحة التحكم</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 فحص شامل لمشاكل لوحة التحكم</h1>";

// 1. فحص الاتصال بقاعدة البيانات
echo "<div class='section'>";
echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
if ($conn->connect_error) {
    echo "<div class='error'>❌ فشل الاتصال: " . $conn->connect_error . "</div>";
    exit();
} else {
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    echo "<div class='info'>📊 قاعدة البيانات: " . $dbname . "</div>";
}
echo "</div>";

// 2. فحص الجداول المطلوبة
echo "<div class='section'>";
echo "<h2>2. فحص الجداول المطلوبة</h2>";

$required_tables = ['users_d', 'articles', 'categories', 'doctors', 'services'];
$existing_tables = [];

$result = $conn->query("SHOW TABLES");
while ($row = $result->fetch_array()) {
    $existing_tables[] = $row[0];
}

foreach ($required_tables as $table) {
    if (in_array($table, $existing_tables)) {
        echo "<div class='success'>✅ جدول '$table' موجود</div>";
    } else {
        echo "<div class='error'>❌ جدول '$table' غير موجود</div>";
    }
}
echo "</div>";

// 3. فحص بنية الجداول والعلاقات
echo "<div class='section'>";
echo "<h2>3. فحص بنية الجداول والعلاقات</h2>";

// فحص جدول users_d
if (in_array('users_d', $existing_tables)) {
    $result = $conn->query("DESCRIBE users_d");
    echo "<h3>جدول users_d:</h3>";
    echo "<table>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عدد المستخدمين
    $count_result = $conn->query("SELECT COUNT(*) as count FROM users_d");
    $count = $count_result->fetch_assoc()['count'];
    echo "<div class='info'>📊 عدد المستخدمين: $count</div>";
}

// فحص جدول articles
if (in_array('articles', $existing_tables)) {
    echo "<h3>جدول articles:</h3>";
    $result = $conn->query("DESCRIBE articles");
    echo "<table>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عدد المقالات
    $count_result = $conn->query("SELECT COUNT(*) as count FROM articles");
    $count = $count_result->fetch_assoc()['count'];
    echo "<div class='info'>📊 عدد المقالات: $count</div>";
}
echo "</div>";

// 4. فحص العلاقات والمراجع الخارجية
echo "<div class='section'>";
echo "<h2>4. فحص العلاقات والمراجع الخارجية</h2>";

// فحص علاقة المقالات بالمستخدمين
try {
    $result = $conn->query("
        SELECT a.id, a.title, a.author_id, u.name as author_name 
        FROM articles a 
        LEFT JOIN users_d u ON a.author_id = u.id 
        LIMIT 5
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>✅ علاقة المقالات بالمستخدمين تعمل</div>";
        echo "<table>";
        echo "<tr><th>ID المقال</th><th>العنوان</th><th>ID المؤلف</th><th>اسم المؤلف</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $author_status = $row['author_name'] ? 'موجود' : 'غير موجود';
            $row_class = $row['author_name'] ? '' : 'style="background-color: #ffebee;"';
            echo "<tr $row_class>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['title']) . "</td>";
            echo "<td>" . $row['author_id'] . "</td>";
            echo "<td>" . ($row['author_name'] ?? '❌ مؤلف غير موجود') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد مقالات أو مشكلة في العلاقة</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص علاقة المقالات: " . $e->getMessage() . "</div>";
}

// فحص علاقة المقالات بالتصنيفات
try {
    $result = $conn->query("
        SELECT a.id, a.title, a.category_id, c.name as category_name 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LIMIT 5
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>✅ علاقة المقالات بالتصنيفات تعمل</div>";
        echo "<table>";
        echo "<tr><th>ID المقال</th><th>العنوان</th><th>ID التصنيف</th><th>اسم التصنيف</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $category_status = $row['category_name'] ? 'موجود' : 'غير موجود';
            $row_class = $row['category_name'] ? '' : 'style="background-color: #ffebee;"';
            echo "<tr $row_class>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['title']) . "</td>";
            echo "<td>" . $row['category_id'] . "</td>";
            echo "<td>" . ($row['category_name'] ?? '❌ تصنيف غير موجود') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد مقالات أو مشكلة في العلاقة</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص علاقة التصنيفات: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 5. فحص البيانات المفقودة أو المتضاربة
echo "<div class='section'>";
echo "<h2>5. فحص البيانات المفقودة أو المتضاربة</h2>";

// فحص المقالات بدون مؤلف صحيح
$orphaned_articles = $conn->query("
    SELECT COUNT(*) as count 
    FROM articles a 
    LEFT JOIN users_d u ON a.author_id = u.id 
    WHERE u.id IS NULL
");
$orphaned_count = $orphaned_articles->fetch_assoc()['count'];

if ($orphaned_count > 0) {
    echo "<div class='error'>❌ يوجد $orphaned_count مقال بدون مؤلف صحيح</div>";
} else {
    echo "<div class='success'>✅ جميع المقالات لها مؤلفون صحيحون</div>";
}

// فحص المقالات بدون تصنيف صحيح
$orphaned_categories = $conn->query("
    SELECT COUNT(*) as count 
    FROM articles a 
    LEFT JOIN categories c ON a.category_id = c.id 
    WHERE c.id IS NULL
");
$orphaned_cat_count = $orphaned_categories->fetch_assoc()['count'];

if ($orphaned_cat_count > 0) {
    echo "<div class='error'>❌ يوجد $orphaned_cat_count مقال بدون تصنيف صحيح</div>";
} else {
    echo "<div class='success'>✅ جميع المقالات لها تصنيفات صحيحة</div>";
}
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

$conn->close();
?>
