<?php
session_start();

// التحقق من وجود البيانات المرسلة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: signin.php");
    exit;
}

// تضمين ملف قاعدة البيانات
include 'config/database.php';

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['username']) || !isset($_POST['password'])) {
    header("Location: signin.php?error=missing_data");
    exit;
}

$username = mysqli_real_escape_string($conn, $_POST['username']);
$password = $_POST['password'];

// البحث عن المستخدم في قاعدة البيانات
$sql = "SELECT * FROM users_d WHERE name='$username' and password='$password'";
$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) == 1) {
    $row = mysqli_fetch_assoc($result);

    // التحقق من كلمة المرور
    $_SESSION['user_id'] = $row['id'];
    $_SESSION['username'] = $row['name'];
    $_SESSION['email'] = $row['email'];

    // التوجيه إلى لوحة التحكم
    header("Location: admin-dashboard.php?login=success");
    exit;

} else {
    // المستخدم غير موجود
    header("Location: signin.php?error=user_not_found");
    exit;
    
}
?>

