<?php
// تحديث عداد المشاهدات للمقالات
header('Content-Type: application/json');
require_once '../config/database.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['article_id']) || !is_numeric($input['article_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid article ID']);
    exit;
}

$articleId = (int)$input['article_id'];

try {
    // تحديث عداد المشاهدات
    $query = "UPDATE articles SET views = views + 1 WHERE id = ? AND status = 'published'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $articleId);
    
    if ($stmt->execute()) {
        // جلب العدد الجديد للمشاهدات
        $selectQuery = "SELECT views FROM articles WHERE id = ?";
        $selectStmt = $conn->prepare($selectQuery);
        $selectStmt->bind_param('i', $articleId);
        $selectStmt->execute();
        $result = $selectStmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            echo json_encode([
                'success' => true,
                'views' => $row['views'],
                'message' => 'Views updated successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Article not found'
            ]);
        }
    } else {
        throw new Exception('Failed to update views');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error',
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
