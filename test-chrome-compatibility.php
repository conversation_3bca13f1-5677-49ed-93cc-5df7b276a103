<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التوافق مع Chrome</title>
    <link rel="stylesheet" href="style/admin-dashboard.css">
    <link rel="stylesheet" href="style/chrome-compatibility.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
        }
        .test-passed {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-failed {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .browser-info {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .feature-test {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .status-icon {
            font-size: 1.2rem;
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
    </style>
</head>
<body class="admin-body">

<div class="test-container">
    <h1>🔍 اختبار التوافق مع متصفح Chrome</h1>
    
    <div class="browser-info">
        <h3>📊 معلومات المتصفح</h3>
        <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
        <p><strong>المتصفح:</strong> <span id="browserName"></span></p>
        <p><strong>الإصدار:</strong> <span id="browserVersion"></span></p>
    </div>

    <div class="test-section" id="cssTest">
        <h3>🎨 اختبار CSS Features</h3>
        <div class="feature-test">
            <span>CSS Grid Support</span>
            <span class="status-icon" id="gridStatus"></span>
        </div>
        <div class="feature-test">
            <span>CSS Flexbox Support</span>
            <span class="status-icon" id="flexStatus"></span>
        </div>
        <div class="feature-test">
            <span>CSS Transforms Support</span>
            <span class="status-icon" id="transformStatus"></span>
        </div>
        <div class="feature-test">
            <span>CSS Transitions Support</span>
            <span class="status-icon" id="transitionStatus"></span>
        </div>
        <div class="feature-test">
            <span>CSS Border Radius Support</span>
            <span class="status-icon" id="borderRadiusStatus"></span>
        </div>
        <div class="feature-test">
            <span>CSS Box Shadow Support</span>
            <span class="status-icon" id="boxShadowStatus"></span>
        </div>
    </div>

    <div class="test-section" id="visualTest">
        <h3>👁️ اختبار العناصر المرئية</h3>
        
        <!-- Test Stats Grid -->
        <h4>Stats Grid Test:</h4>
        <div class="stats-grid" style="margin: 20px 0;">
            <div class="stat-card">
                <div class="stat-icon" style="background: #d4af37; color: white; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-info">
                    <h3>المقالات</h3>
                    <p class="stat-number">25</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="background: #28a745; color: white; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-info">
                    <h3>التصنيفات</h3>
                    <p class="stat-number">8</p>
                </div>
            </div>
        </div>

        <!-- Test Quick Analytics -->
        <h4>Quick Analytics Test:</h4>
        <div class="quick-analytics" style="margin: 20px 0;">
            <div class="quick-chart-card">
                <h3>اختبار الرسم البياني</h3>
                <div style="width: 100%; height: 200px; background: linear-gradient(45deg, #d4af37, #f4d03f); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                    Chart Placeholder
                </div>
            </div>
        </div>

        <!-- Test Buttons -->
        <h4>Buttons Test:</h4>
        <div style="margin: 20px 0;">
            <button class="btn" style="background: #d4af37; color: white; padding: 10px 20px; border: none; border-radius: 8px; margin: 5px;">
                زر اختبار
            </button>
            <button class="btn" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 8px; margin: 5px;">
                زر ثاني
            </button>
        </div>
    </div>

    <div class="test-section" id="performanceTest">
        <h3>⚡ اختبار الأداء</h3>
        <div class="feature-test">
            <span>Page Load Time</span>
            <span id="loadTime"></span>
        </div>
        <div class="feature-test">
            <span>DOM Ready Time</span>
            <span id="domTime"></span>
        </div>
    </div>

    <div class="test-section" id="recommendationsTest">
        <h3>💡 التوصيات</h3>
        <div id="recommendations"></div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="admin-dashboard.php" class="btn" style="background: #d4af37; color: white; padding: 15px 30px; border-radius: 8px; text-decoration: none; display: inline-block;">
            🚀 انتقل إلى لوحة التحكم
        </a>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Browser Detection
    const userAgent = navigator.userAgent;
    document.getElementById('userAgent').textContent = userAgent;
    
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';
    
    if (userAgent.indexOf('Chrome') > -1) {
        browserName = 'Google Chrome';
        const match = userAgent.match(/Chrome\/(\d+)/);
        if (match) browserVersion = match[1];
    } else if (userAgent.indexOf('Edge') > -1) {
        browserName = 'Microsoft Edge';
        const match = userAgent.match(/Edge\/(\d+)/);
        if (match) browserVersion = match[1];
    } else if (userAgent.indexOf('Firefox') > -1) {
        browserName = 'Mozilla Firefox';
        const match = userAgent.match(/Firefox\/(\d+)/);
        if (match) browserVersion = match[1];
    }
    
    document.getElementById('browserName').textContent = browserName;
    document.getElementById('browserVersion').textContent = browserVersion;
    
    // CSS Feature Tests
    function testCSSFeature(property, value, elementId) {
        const testElement = document.createElement('div');
        testElement.style[property] = value;
        const supported = testElement.style[property] === value;
        
        const statusElement = document.getElementById(elementId);
        if (supported) {
            statusElement.innerHTML = '<i class="fas fa-check passed"></i>';
            statusElement.parentElement.parentElement.classList.add('test-passed');
        } else {
            statusElement.innerHTML = '<i class="fas fa-times failed"></i>';
            statusElement.parentElement.parentElement.classList.add('test-failed');
        }
        
        return supported;
    }
    
    // Test CSS Features
    const gridSupported = testCSSFeature('display', 'grid', 'gridStatus');
    const flexSupported = testCSSFeature('display', 'flex', 'flexStatus');
    const transformSupported = testCSSFeature('transform', 'translateX(10px)', 'transformStatus');
    const transitionSupported = testCSSFeature('transition', 'all 0.3s ease', 'transitionStatus');
    const borderRadiusSupported = testCSSFeature('borderRadius', '10px', 'borderRadiusStatus');
    const boxShadowSupported = testCSSFeature('boxShadow', '0 2px 4px rgba(0,0,0,0.1)', 'boxShadowStatus');
    
    // Performance Tests
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    const domTime = performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart;
    
    document.getElementById('loadTime').textContent = loadTime + 'ms';
    document.getElementById('domTime').textContent = domTime + 'ms';
    
    // Recommendations
    const recommendations = [];
    
    if (browserName === 'Google Chrome') {
        recommendations.push('✅ أنت تستخدم Chrome - جميع الميزات مدعومة');
        if (parseInt(browserVersion) < 90) {
            recommendations.push('⚠️ يُنصح بتحديث Chrome لأحدث إصدار');
        }
    } else if (browserName === 'Microsoft Edge') {
        recommendations.push('✅ Edge متوافق بشكل جيد مع النظام');
    } else {
        recommendations.push('⚠️ للحصول على أفضل تجربة، استخدم Chrome أو Edge');
    }
    
    if (!gridSupported) {
        recommendations.push('❌ CSS Grid غير مدعوم - قد تواجه مشاكل في التخطيط');
    }
    
    if (!flexSupported) {
        recommendations.push('❌ CSS Flexbox غير مدعوم - قد تواجه مشاكل في التخطيط');
    }
    
    if (loadTime > 3000) {
        recommendations.push('⚠️ وقت التحميل بطيء - تحقق من اتصال الإنترنت');
    }
    
    const recommendationsDiv = document.getElementById('recommendations');
    recommendations.forEach(rec => {
        const p = document.createElement('p');
        p.textContent = rec;
        p.style.margin = '10px 0';
        p.style.padding = '10px';
        p.style.background = '#f8f9fa';
        p.style.borderRadius = '4px';
        recommendationsDiv.appendChild(p);
    });
});
</script>

</body>
</html>
