/* Chrome Compatibility CSS */
/* إصلاحات خاصة بمتصفح Chrome */

/* Font Rendering Fixes */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Flexbox Fixes for Chrome */
.admin-nav,
.stat-card,
.quick-chart-card,
.analytics-card,
.table-actions,
.form-group,
.btn-group {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -moz-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
}

/* Grid Layout Fixes */
.stats-grid,
.analytics-grid,
.quick-analytics {
    display: -ms-grid !important;
    display: grid !important;
}

/* Border Radius Fixes */
.stat-card,
.analytics-card,
.quick-chart-card,
.btn,
.form-control,
.admin-header {
    -webkit-border-radius: inherit;
    -moz-border-radius: inherit;
    border-radius: inherit;
}

/* Box Shadow Fixes */
.stat-card,
.analytics-card,
.quick-chart-card,
.admin-header {
    -webkit-box-shadow: inherit;
    -moz-box-shadow: inherit;
    box-shadow: inherit;
}

/* Transform Fixes */
.stat-card:hover,
.analytics-card:hover,
.quick-chart-card:hover {
    -webkit-transform: inherit;
    -moz-transform: inherit;
    -ms-transform: inherit;
    -o-transform: inherit;
    transform: inherit;
}

/* Transition Fixes */
.stat-card,
.analytics-card,
.quick-chart-card,
.btn {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

/* Background Gradient Fixes */
.admin-header {
    background: #d4af37;
    background: -webkit-gradient(linear, left top, right bottom, from(#d4af37), to(#b8860b));
    background: -webkit-linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    background: -moz-linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    background: -o-linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
}

/* Chrome Specific Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #d4af37;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #b8860b;
}

/* Chrome Input Styling Fixes */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
}

/* Chrome Button Styling Fixes */
.btn {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Chrome Table Fixes */
.data-table {
    border-collapse: collapse;
    -webkit-border-horizontal-spacing: 0;
    -webkit-border-vertical-spacing: 0;
}

/* Chrome Canvas Fixes for Charts */
canvas {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    max-width: 100% !important;
    height: auto !important;
}

/* Chrome Animation Fixes */
@-webkit-keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@-moz-keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.admin-section {
    -webkit-animation: fadeIn 0.3s ease;
    -moz-animation: fadeIn 0.3s ease;
    animation: fadeIn 0.3s ease;
}

/* Chrome Gap Property Fallback */
.stats-grid {
    gap: 1.5rem;
    grid-gap: 1.5rem; /* Fallback */
}

.quick-analytics {
    gap: 1.5rem;
    grid-gap: 1.5rem; /* Fallback */
}

/* Chrome Flex Gap Fallback */
.admin-nav > * + *,
.stat-card > * + *,
.table-actions > * + * {
    margin-right: 1rem;
}

/* Chrome Text Selection */
::selection {
    background: rgba(212, 175, 55, 0.3);
    color: #333;
}

::-moz-selection {
    background: rgba(212, 175, 55, 0.3);
    color: #333;
}

/* Chrome Print Styles */
@media print {
    .admin-header,
    .admin-sidebar {
        display: none !important;
    }
    
    .admin-main {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* Chrome Mobile Fixes */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        -ms-grid-columns: 1fr;
    }
    
    .quick-analytics {
        grid-template-columns: 1fr;
        -ms-grid-columns: 1fr;
    }
}

/* Chrome Focus Fixes */
input:focus,
textarea:focus,
select:focus,
button:focus {
    outline: 2px solid #d4af37;
    outline-offset: 2px;
    -webkit-box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    -moz-box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Chrome Performance Optimizations */
.stat-card,
.analytics-card,
.quick-chart-card {
    will-change: transform;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* Chrome Text Rendering */
h1, h2, h3, h4, h5, h6 {
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "liga", "kern";
    -moz-font-feature-settings: "liga", "kern";
    font-feature-settings: "liga", "kern";
}
