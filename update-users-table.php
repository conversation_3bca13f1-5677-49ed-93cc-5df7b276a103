<?php
// تحديث جدول المستخدمين من users إلى users_d
require_once 'config/database.php';

echo "<h1>🔄 تحديث جدول المستخدمين</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ فشل الاتصال: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
}

echo "<h2>2. فحص الجداول الموجودة</h2>";

// فحص وجود جدول users القديم
$users_exists = false;
$users_d_exists = false;

$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows > 0) {
    $users_exists = true;
    echo "<p style='color: orange;'>⚠️ جدول 'users' القديم موجود</p>";
} else {
    echo "<p style='color: green;'>✅ جدول 'users' القديم غير موجود</p>";
}

$result = $conn->query("SHOW TABLES LIKE 'users_d'");
if ($result->num_rows > 0) {
    $users_d_exists = true;
    echo "<p style='color: green;'>✅ جدول 'users_d' الجديد موجود</p>";
} else {
    echo "<p style='color: red;'>❌ جدول 'users_d' الجديد غير موجود</p>";
}

echo "<h2>3. تطبيق التحديثات</h2>";

// إذا كان جدول users موجود و users_d غير موجود، انسخ البيانات
if ($users_exists && !$users_d_exists) {
    echo "<p style='color: blue;'>🔄 نسخ البيانات من جدول 'users' إلى 'users_d'...</p>";
    
    // إنشاء جدول users_d
    $create_query = "CREATE TABLE `users_d` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($create_query)) {
        echo "<p style='color: green;'>✅ تم إنشاء جدول 'users_d' بنجاح</p>";
        
        // نسخ البيانات
        $copy_query = "INSERT INTO users_d (id, name, email, password) SELECT id, name, email, password FROM users";
        if ($conn->query($copy_query)) {
            echo "<p style='color: green;'>✅ تم نسخ البيانات بنجاح</p>";
            
            // عرض عدد السجلات المنسوخة
            $count_result = $conn->query("SELECT COUNT(*) as count FROM users_d");
            $count = $count_result->fetch_assoc()['count'];
            echo "<p style='color: blue;'>📊 تم نسخ $count مستخدم</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في نسخ البيانات: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء جدول 'users_d': " . $conn->error . "</p>";
    }
}

// إذا كان جدول users_d غير موجود، أنشئه مع بيانات افتراضية
if (!$users_d_exists) {
    echo "<p style='color: blue;'>🔄 إنشاء جدول 'users_d' مع بيانات افتراضية...</p>";
    
    $create_query = "CREATE TABLE IF NOT EXISTS `users_d` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($create_query)) {
        echo "<p style='color: green;'>✅ تم إنشاء جدول 'users_d'</p>";
        
        // إضافة بيانات افتراضية
        $default_users = [
            ['admin', '<EMAIL>', 'admin123'],
            ['doctor1', '<EMAIL>', 'doctor123']
        ];
        
        foreach ($default_users as $user) {
            $stmt = $conn->prepare("INSERT IGNORE INTO users_d (name, email, password) VALUES (?, ?, ?)");
            $stmt->bind_param("sss", $user[0], $user[1], $user[2]);
            $stmt->execute();
        }
        
        echo "<p style='color: green;'>✅ تم إضافة المستخدمين الافتراضيين</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء جدول 'users_d': " . $conn->error . "</p>";
    }
}

echo "<h2>4. فحص البيانات النهائية</h2>";

// عرض محتويات جدول users_d
$result = $conn->query("SELECT * FROM users_d");
if ($result->num_rows > 0) {
    echo "<p style='color: green;'>✅ جدول 'users_d' يحتوي على " . $result->num_rows . " مستخدم:</p>";
    echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>الاسم</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>البريد الإلكتروني</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>كلمة المرور</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['id'] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($row['email']) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($row['password']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ جدول 'users_d' فارغ</p>";
}

echo "<h2>5. اختبار تسجيل الدخول</h2>";

// اختبار تسجيل الدخول مع المستخدمين الجدد
$test_users = [
    ['admin', 'admin123'],
    ['doctor1', 'doctor123']
];

foreach ($test_users as $test_user) {
    $username = $test_user[0];
    $password = $test_user[1];
    
    $stmt = $conn->prepare("SELECT * FROM users_d WHERE name = ? AND password = ?");
    $stmt->bind_param("ss", $username, $password);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ تسجيل الدخول يعمل للمستخدم: $username</p>";
    } else {
        echo "<p style='color: red;'>❌ تسجيل الدخول لا يعمل للمستخدم: $username</p>";
    }
}

echo "<h2>6. تحديث الملفات المرجعية</h2>";

$updated_files = [
    'php/dashboard-data.php' => 'ملف بيانات لوحة التحكم',
    'admin-dashboard.php' => 'لوحة التحكم الرئيسية',
    'database/den_blog.sql' => 'ملف قاعدة البيانات',
    'database/fix_database.sql' => 'ملف إصلاح قاعدة البيانات',
    'test-complete-dashboard.php' => 'ملف اختبار شامل'
];

echo "<p style='color: blue;'>📝 الملفات التي تم تحديثها لاستخدام جدول 'users_d':</p>";
echo "<ul>";
foreach ($updated_files as $file => $description) {
    if (file_exists($file)) {
        echo "<li style='color: green;'>✅ $description ($file)</li>";
    } else {
        echo "<li style='color: red;'>❌ $description ($file) - غير موجود</li>";
    }
}
echo "</ul>";

echo "<h2>7. النتيجة النهائية</h2>";

echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🎉 تم تحديث جدول المستخدمين بنجاح!</h3>";
echo "<p><strong>التغييرات المطبقة:</strong></p>";
echo "<ul>";
echo "<li>✅ تم إنشاء/تحديث جدول 'users_d'</li>";
echo "<li>✅ تم نسخ البيانات من الجدول القديم (إن وجد)</li>";
echo "<li>✅ تم تحديث جميع الملفات لاستخدام 'users_d'</li>";
echo "<li>✅ تم اختبار تسجيل الدخول</li>";
echo "<li>✅ النظام جاهز للاستخدام</li>";
echo "</ul>";
echo "</div>";

echo "<h2>8. بيانات تسجيل الدخول</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px;'>";
echo "<p><strong>يمكنك الآن تسجيل الدخول باستخدام:</strong></p>";
echo "<ul>";
echo "<li><strong>المدير:</strong> admin / admin123</li>";
echo "<li><strong>الطبيب:</strong> doctor1 / doctor123</li>";
echo "</ul>";
echo "<p><a href='signin.php' style='color: #d4af37; font-weight: bold;'>🔗 صفحة تسجيل الدخول</a></p>";
echo "<p><a href='admin-dashboard.php' style='color: #0066cc; font-weight: bold;'>🏠 لوحة التحكم</a></p>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

h1 {
    color: #d4af37;
    border-bottom: 3px solid #d4af37;
    padding-bottom: 15px;
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

h2 {
    color: #2c3e50;
    margin-top: 30px;
    border-right: 4px solid #d4af37;
    padding-right: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

h3 {
    color: #34495e;
    margin-top: 20px;
    font-size: 1.1rem;
}

p {
    margin: 8px 0;
    line-height: 1.6;
}

ul {
    line-height: 1.8;
}

a {
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>
