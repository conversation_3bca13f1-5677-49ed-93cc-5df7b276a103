<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// الحصول على نوع العملية
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'add_article':
            addArticle();
            break;
        case 'edit_article':
            editArticle();
            break;
        case 'delete_article':
            deleteArticle();
            break;
        case 'add_category':
            addCategory();
            break;
        case 'edit_category':
            editCategory();
            break;
        case 'delete_category':
            deleteCategory();
            break;
        case 'add_doctor':
            addDoctor();
            break;
        case 'edit_doctor':
            editDoctor();
            break;
        case 'delete_doctor':
            deleteDoctor();
            break;
        case 'add_service':
            addService();
            break;
        case 'edit_service':
            editService();
            break;
        case 'delete_service':
            deleteService();
            break;
        case 'bulk_change_status':
            bulkChangeStatus();
            break;
        case 'bulk_change_category':
            bulkChangeCategory();
            break;
        case 'bulk_delete':
            bulkDelete();
            break;
        case 'duplicate_article':
            duplicateArticle();
            break;
        case 'preview_article':
            previewArticle();
            break;
        default:
            throw new Exception('عملية غير صحيحة');
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// إضافة مقال جديد
function addArticle() {
    global $conn;
    
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $category_id = (int)($_POST['category_id'] ?? 0);
    $status = $_POST['status'] ?? 'draft';
    $author_id = $_SESSION['user_id'];
    
    // التحقق من البيانات
    if (empty($title)) {
        throw new Exception('عنوان المقال مطلوب');
    }
    
    if (empty($content)) {
        throw new Exception('محتوى المقال مطلوب');
    }
    
    if ($category_id <= 0) {
        throw new Exception('يجب اختيار تصنيف صحيح');
    }
    
    // إدراج المقال
    $query = "INSERT INTO articles (title, content, category_id, author_id, status) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssiis', $title, $content, $category_id, $author_id, $status);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم إضافة المقال بنجاح']);
    } else {
        throw new Exception('فشل في إضافة المقال');
    }
}

// إضافة تصنيف جديد
function addCategory() {
    global $conn;
    
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    // التحقق من البيانات
    if (empty($name)) {
        throw new Exception('اسم التصنيف مطلوب');
    }
    
    // التحقق من عدم وجود تصنيف بنفس الاسم
    $checkQuery = "SELECT id FROM categories WHERE name = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param('s', $name);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('يوجد تصنيف بهذا الاسم مسبقاً');
    }
    
    // إدراج التصنيف
    $query = "INSERT INTO categories (name, description) VALUES (?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ss', $name, $description);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم إضافة التصنيف بنجاح']);
    } else {
        throw new Exception('فشل في إضافة التصنيف');
    }
}

// إضافة طبيب جديد
function addDoctor() {
    global $conn;
    
    $name = trim($_POST['name'] ?? '');
    $specialty = trim($_POST['specialty'] ?? '');
    $experience = (int)($_POST['experience'] ?? 0);
    $phone = trim($_POST['phone'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    
    // التحقق من البيانات
    if (empty($name)) {
        throw new Exception('اسم الطبيب مطلوب');
    }
    
    if (empty($specialty)) {
        throw new Exception('تخصص الطبيب مطلوب');
    }
    
    // إدراج الطبيب
    $query = "INSERT INTO doctors (name, specialty, experience, phone, bio) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssiss', $name, $specialty, $experience, $phone, $bio);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم إضافة الطبيب بنجاح']);
    } else {
        throw new Exception('فشل في إضافة الطبيب');
    }
}

// إضافة خدمة جديدة
function addService() {
    global $conn;
    
    $name = trim($_POST['name'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $price = (float)($_POST['price'] ?? 0);
    $duration = trim($_POST['duration'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $icon = trim($_POST['icon'] ?? 'fas fa-tooth');
    
    // التحقق من البيانات
    if (empty($name)) {
        throw new Exception('اسم الخدمة مطلوب');
    }
    
    if (empty($category)) {
        throw new Exception('فئة الخدمة مطلوبة');
    }
    
    // إدراج الخدمة
    $query = "INSERT INTO services (name, category, price, duration, description, icon) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssdsss', $name, $category, $price, $duration, $description, $icon);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم إضافة الخدمة بنجاح']);
    } else {
        throw new Exception('فشل في إضافة الخدمة');
    }
}

// حذف مقال
function deleteArticle() {
    global $conn;
    
    $id = (int)($_POST['id'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('معرف المقال غير صحيح');
    }
    
    $query = "DELETE FROM articles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم حذف المقال بنجاح']);
    } else {
        throw new Exception('فشل في حذف المقال');
    }
}

// حذف تصنيف
function deleteCategory() {
    global $conn;
    
    $id = (int)($_POST['id'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('معرف التصنيف غير صحيح');
    }
    
    // التحقق من عدم وجود مقالات مرتبطة بالتصنيف
    $checkQuery = "SELECT COUNT(*) as count FROM articles WHERE category_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param('i', $id);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    $count = $result->fetch_assoc()['count'];
    
    if ($count > 0) {
        throw new Exception('لا يمكن حذف التصنيف لوجود مقالات مرتبطة به');
    }
    
    $query = "DELETE FROM categories WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم حذف التصنيف بنجاح']);
    } else {
        throw new Exception('فشل في حذف التصنيف');
    }
}

// حذف طبيب
function deleteDoctor() {
    global $conn;
    
    $id = (int)($_POST['id'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('معرف الطبيب غير صحيح');
    }
    
    $query = "DELETE FROM doctors WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم حذف الطبيب بنجاح']);
    } else {
        throw new Exception('فشل في حذف الطبيب');
    }
}

// حذف خدمة
function deleteService() {
    global $conn;
    
    $id = (int)($_POST['id'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('معرف الخدمة غير صحيح');
    }
    
    $query = "DELETE FROM services WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم حذف الخدمة بنجاح']);
    } else {
        throw new Exception('فشل في حذف الخدمة');
    }
}

// تعديل مقال
function editArticle() {
    global $conn;

    $id = (int)($_POST['id'] ?? 0);
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $category_id = (int)($_POST['category_id'] ?? 0);
    $status = $_POST['status'] ?? 'draft';

    // التحقق من البيانات
    if ($id <= 0) {
        throw new Exception('معرف المقال غير صحيح');
    }

    if (empty($title)) {
        throw new Exception('عنوان المقال مطلوب');
    }

    if (empty($content)) {
        throw new Exception('محتوى المقال مطلوب');
    }

    if ($category_id <= 0) {
        throw new Exception('يجب اختيار تصنيف صحيح');
    }

    // تحديث المقال
    $query = "UPDATE articles SET title = ?, content = ?, category_id = ?, status = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssisi', $title, $content, $category_id, $status, $id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث المقال بنجاح']);
    } else {
        throw new Exception('فشل في تحديث المقال');
    }
}

// تعديل تصنيف
function editCategory() {
    global $conn;

    $id = (int)($_POST['id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');

    // التحقق من البيانات
    if ($id <= 0) {
        throw new Exception('معرف التصنيف غير صحيح');
    }

    if (empty($name)) {
        throw new Exception('اسم التصنيف مطلوب');
    }

    // التحقق من عدم وجود تصنيف آخر بنفس الاسم
    $checkQuery = "SELECT id FROM categories WHERE name = ? AND id != ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param('si', $name, $id);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        throw new Exception('يوجد تصنيف آخر بهذا الاسم');
    }

    // تحديث التصنيف
    $query = "UPDATE categories SET name = ?, description = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssi', $name, $description, $id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث التصنيف بنجاح']);
    } else {
        throw new Exception('فشل في تحديث التصنيف');
    }
}

// تعديل طبيب
function editDoctor() {
    global $conn;

    $id = (int)($_POST['id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $specialty = trim($_POST['specialty'] ?? '');
    $experience = (int)($_POST['experience'] ?? 0);
    $phone = trim($_POST['phone'] ?? '');
    $bio = trim($_POST['bio'] ?? '');

    // التحقق من البيانات
    if ($id <= 0) {
        throw new Exception('معرف الطبيب غير صحيح');
    }

    if (empty($name)) {
        throw new Exception('اسم الطبيب مطلوب');
    }

    if (empty($specialty)) {
        throw new Exception('تخصص الطبيب مطلوب');
    }

    // تحديث الطبيب
    $query = "UPDATE doctors SET name = ?, specialty = ?, experience = ?, phone = ?, bio = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssissi', $name, $specialty, $experience, $phone, $bio, $id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث بيانات الطبيب بنجاح']);
    } else {
        throw new Exception('فشل في تحديث بيانات الطبيب');
    }
}

// تعديل خدمة
function editService() {
    global $conn;

    $id = (int)($_POST['id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $price = (float)($_POST['price'] ?? 0);
    $duration = trim($_POST['duration'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $icon = trim($_POST['icon'] ?? 'fas fa-tooth');

    // التحقق من البيانات
    if ($id <= 0) {
        throw new Exception('معرف الخدمة غير صحيح');
    }

    if (empty($name)) {
        throw new Exception('اسم الخدمة مطلوب');
    }

    if (empty($category)) {
        throw new Exception('فئة الخدمة مطلوبة');
    }

    // تحديث الخدمة
    $query = "UPDATE services SET name = ?, category = ?, price = ?, duration = ?, description = ?, icon = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssdssi', $name, $category, $price, $duration, $description, $icon, $id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث الخدمة بنجاح']);
    } else {
        throw new Exception('فشل في تحديث الخدمة');
    }
}

// العمليات المتعددة للمقالات
function bulkChangeStatus() {
    global $conn;

    $articleIds = json_decode($_POST['article_ids'] ?? '[]', true);
    $newStatus = $_POST['status'] ?? '';

    if (empty($articleIds) || !is_array($articleIds)) {
        throw new Exception('لم يتم تحديد مقالات');
    }

    if (!in_array($newStatus, ['published', 'draft', 'scheduled'])) {
        throw new Exception('حالة غير صحيحة');
    }

    // تحديث حالة المقالات المحددة
    $placeholders = str_repeat('?,', count($articleIds) - 1) . '?';
    $query = "UPDATE articles SET status = ? WHERE id IN ($placeholders)";

    $stmt = $conn->prepare($query);
    $params = array_merge([$newStatus], $articleIds);
    $types = 's' . str_repeat('i', count($articleIds));
    $stmt->bind_param($types, ...$params);

    if ($stmt->execute()) {
        $affectedRows = $stmt->affected_rows;
        echo json_encode(['success' => true, 'message' => "تم تحديث $affectedRows مقال بنجاح"]);
    } else {
        throw new Exception('فشل في تحديث المقالات');
    }
}

function bulkChangeCategory() {
    global $conn;

    $articleIds = json_decode($_POST['article_ids'] ?? '[]', true);
    $categoryId = (int)($_POST['category_id'] ?? 0);

    if (empty($articleIds) || !is_array($articleIds)) {
        throw new Exception('لم يتم تحديد مقالات');
    }

    if ($categoryId <= 0) {
        throw new Exception('معرف التصنيف غير صحيح');
    }

    // التحقق من وجود التصنيف
    $checkQuery = "SELECT id FROM categories WHERE id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param('i', $categoryId);
    $checkStmt->execute();

    if ($checkStmt->get_result()->num_rows === 0) {
        throw new Exception('التصنيف غير موجود');
    }

    // تحديث تصنيف المقالات المحددة
    $placeholders = str_repeat('?,', count($articleIds) - 1) . '?';
    $query = "UPDATE articles SET category_id = ? WHERE id IN ($placeholders)";

    $stmt = $conn->prepare($query);
    $params = array_merge([$categoryId], $articleIds);
    $types = 'i' . str_repeat('i', count($articleIds));
    $stmt->bind_param($types, ...$params);

    if ($stmt->execute()) {
        $affectedRows = $stmt->affected_rows;
        echo json_encode(['success' => true, 'message' => "تم تحديث تصنيف $affectedRows مقال بنجاح"]);
    } else {
        throw new Exception('فشل في تحديث تصنيف المقالات');
    }
}

function bulkDelete() {
    global $conn;

    $articleIds = json_decode($_POST['article_ids'] ?? '[]', true);

    if (empty($articleIds) || !is_array($articleIds)) {
        throw new Exception('لم يتم تحديد مقالات');
    }

    // حذف المقالات المحددة
    $placeholders = str_repeat('?,', count($articleIds) - 1) . '?';
    $query = "DELETE FROM articles WHERE id IN ($placeholders)";

    $stmt = $conn->prepare($query);
    $types = str_repeat('i', count($articleIds));
    $stmt->bind_param($types, ...$articleIds);

    if ($stmt->execute()) {
        $affectedRows = $stmt->affected_rows;
        echo json_encode(['success' => true, 'message' => "تم حذف $affectedRows مقال بنجاح"]);
    } else {
        throw new Exception('فشل في حذف المقالات');
    }
}

function duplicateArticle() {
    global $conn;

    $id = (int)($_POST['id'] ?? 0);

    if ($id <= 0) {
        throw new Exception('معرف المقال غير صحيح');
    }

    // جلب بيانات المقال الأصلي
    $query = "SELECT * FROM articles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('المقال غير موجود');
    }

    $article = $result->fetch_assoc();

    // إنشاء نسخة جديدة
    $newTitle = $article['title'] . ' - نسخة';
    $insertQuery = "INSERT INTO articles (title, content, category_id, author_id, status) VALUES (?, ?, ?, ?, 'draft')";
    $insertStmt = $conn->prepare($insertQuery);
    $insertStmt->bind_param('ssii', $newTitle, $article['content'], $article['category_id'], $_SESSION['user_id']);

    if ($insertStmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم إنشاء نسخة من المقال بنجاح']);
    } else {
        throw new Exception('فشل في إنشاء نسخة من المقال');
    }
}

function previewArticle() {
    $title = $_POST['title'] ?? 'معاينة المقال';
    $content = $_POST['content'] ?? '';
    $excerpt = $_POST['excerpt'] ?? '';

    // إنشاء HTML للمعاينة
    $html = '<!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($title) . '</title>
        <style>
            body {
                font-family: "Cairo", Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 2rem;
                background: #f8f9fa;
                color: #333;
            }
            .preview-container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 2rem;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .preview-header {
                border-bottom: 2px solid #d4af37;
                padding-bottom: 1rem;
                margin-bottom: 2rem;
            }
            .preview-title {
                color: #d4af37;
                font-size: 2rem;
                font-weight: bold;
                margin: 0;
            }
            .preview-excerpt {
                color: #666;
                font-style: italic;
                margin-top: 1rem;
            }
            .preview-content {
                font-size: 1.1rem;
                line-height: 1.8;
            }
            .preview-content p {
                margin-bottom: 1rem;
            }
        </style>
    </head>
    <body>
        <div class="preview-container">
            <div class="preview-header">
                <h1 class="preview-title">' . htmlspecialchars($title) . '</h1>';

    if (!empty($excerpt)) {
        $html .= '<div class="preview-excerpt">' . htmlspecialchars($excerpt) . '</div>';
    }

    $html .= '</div>
            <div class="preview-content">' . nl2br(htmlspecialchars($content)) . '</div>
        </div>
    </body>
    </html>';

    echo $html;
}
?>
