// Articles Page JavaScript

// Sample articles data (in a real application, this would come from a database)
const articlesData = [
    {
        id: 1,
        title: "أهمية تنظيف الأسنان اليومي",
        excerpt: "تعرف على الطرق الصحيحة لتنظيف الأسنان والحفاظ على صحة الفم واللثة من خلال العادات اليومية البسيطة...",
        category: "prevention",
        categoryName: "الوقاية",
        author: "د. أحمد محمد",
        date: "2024-12-20",
        views: 1250,
        icon: "fas fa-tooth"
    },
    {
        id: 2,
        title: "زراعة الأسنان: الحل الأمثل للأسنان المفقودة",
        excerpt: "دليل شامل حول زراعة الأسنان، المراحل، التكلفة، والنتائج المتوقعة لاستعادة ابتسامتك الطبيعية...",
        category: "treatment",
        categoryName: "العلاج",
        author: "د. فاطمة علي",
        date: "2024-12-18",
        views: 980,
        icon: "fas fa-user-md"
    },
    {
        id: 3,
        title: "تبييض الأسنان: الطرق الآمنة والفعالة",
        excerpt: "اكتشف أحدث تقنيات تبييض الأسنان الآمنة والنصائح للحفاظ على بياض أسنانك لفترة أطول...",
        category: "cosmetic",
        categoryName: "التجميل",
        author: "د. محمد حسن",
        date: "2024-12-16",
        views: 1450,
        icon: "fas fa-smile"
    },
    {
        id: 4,
        title: "العناية بأسنان الأطفال من البداية",
        excerpt: "نصائح مهمة للآباء حول كيفية العناية بأسنان الأطفال منذ الولادة وحتى سن المراهقة...",
        category: "children",
        categoryName: "أسنان الأطفال",
        author: "د. فاطمة علي",
        date: "2024-12-14",
        views: 1120,
        icon: "fas fa-baby"
    },
    {
        id: 5,
        title: "تقويم الأسنان: متى وكيف؟",
        excerpt: "دليل شامل حول تقويم الأسنان، الأنواع المختلفة، والعمر المناسب لبدء العلاج...",
        category: "treatment",
        categoryName: "العلاج",
        author: "د. أحمد محمد",
        date: "2024-12-12",
        views: 890,
        icon: "fas fa-shield-alt"
    },
    {
        id: 6,
        title: "الأطعمة المفيدة والضارة لصحة الأسنان",
        excerpt: "تعرف على الأطعمة التي تقوي أسنانك وتلك التي يجب تجنبها للحفاظ على صحة الفم...",
        category: "prevention",
        categoryName: "الوقاية",
        author: "د. محمد حسن",
        date: "2024-12-10",
        views: 760,
        icon: "fas fa-apple-alt"
    },
    {
        id: 7,
        title: "علاج التهاب اللثة والوقاية منه",
        excerpt: "أسباب التهاب اللثة وطرق العلاج والوقاية للحفاظ على صحة اللثة...",
        category: "treatment",
        categoryName: "العلاج",
        author: "د. أحمد محمد",
        date: "2024-12-08",
        views: 650,
        icon: "fas fa-heartbeat"
    },
    {
        id: 8,
        title: "التركيبات السنية: الأنواع والفوائد",
        excerpt: "دليل شامل حول التركيبات السنية الثابتة والمتحركة وكيفية اختيار الأنسب...",
        category: "treatment",
        categoryName: "العلاج",
        author: "د. فاطمة علي",
        date: "2024-12-06",
        views: 720,
        icon: "fas fa-crown"
    },
    {
        id: 9,
        title: "نصائح للتخلص من رائحة الفم الكريهة",
        excerpt: "أسباب رائحة الفم الكريهة وطرق العلاج والوقاية الفعالة...",
        category: "prevention",
        categoryName: "الوقاية",
        author: "د. محمد حسن",
        date: "2024-12-04",
        views: 580,
        icon: "fas fa-wind"
    },
    {
        id: 10,
        title: "الحشوات التجميلية: البديل الأمثل للحشوات المعدنية",
        excerpt: "مميزات الحشوات التجميلية وكيفية العناية بها للحصول على أفضل النتائج...",
        category: "cosmetic",
        categoryName: "التجميل",
        author: "د. أحمد محمد",
        date: "2024-12-02",
        views: 490,
        icon: "fas fa-gem"
    },
    {
        id: 11,
        title: "متى يحتاج طفلك لزيارة طبيب الأسنان؟",
        excerpt: "العلامات التي تدل على ضرورة زيارة طبيب الأسنان للأطفال...",
        category: "children",
        categoryName: "أسنان الأطفال",
        author: "د. فاطمة علي",
        date: "2024-11-30",
        views: 820,
        icon: "fas fa-stethoscope"
    },
    {
        id: 12,
        title: "العناية بالأسنان أثناء الحمل",
        excerpt: "نصائح مهمة للحوامل للعناية بصحة الأسنان واللثة خلال فترة الحمل...",
        category: "prevention",
        categoryName: "الوقاية",
        author: "د. فاطمة علي",
        date: "2024-11-28",
        views: 950,
        icon: "fas fa-female"
    }
];

// Pagination settings
const articlesPerPage = 6;
let currentPage = 1;
let filteredArticles = [...articlesData];
let currentCategory = 'all';
let currentSort = 'newest';

// DOM elements
const articlesGrid = document.getElementById('articlesGrid');
const pagination = document.getElementById('pagination');
const pageNumbers = document.getElementById('pageNumbers');
const prevPage = document.getElementById('prevPage');
const nextPage = document.getElementById('nextPage');
const currentPageInfo = document.getElementById('currentPageInfo');
const totalPagesInfo = document.getElementById('totalPagesInfo');
const resultsCount = document.getElementById('resultsCount');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const sortSelect = document.getElementById('sortSelect');
const categorySelect = document.getElementById('categorySelect');

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    renderArticles();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchBtn.addEventListener('click', handleSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });

    // Sort functionality
    sortSelect.addEventListener('change', function() {
        currentSort = this.value;
        sortArticles();
        currentPage = 1;
        renderArticles();
    });

    // Category filtering
    categorySelect.addEventListener('change', function() {
        currentCategory = this.value;
        filterArticles();
        currentPage = 1;
        renderArticles();
    });

    // Pagination
    prevPage.addEventListener('click', function(e) {
        e.preventDefault();
        if (currentPage > 1) {
            currentPage--;
            renderArticles();
            scrollToTop();
        }
    });

    nextPage.addEventListener('click', function(e) {
        e.preventDefault();
        const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderArticles();
            scrollToTop();
        }
    });
}

// Handle search
function handleSearch() {
    const searchTerm = searchInput.value.trim().toLowerCase();

    if (searchTerm === '') {
        filteredArticles = [...articlesData];
    } else {
        filteredArticles = articlesData.filter(article =>
            article.title.toLowerCase().includes(searchTerm) ||
            article.excerpt.toLowerCase().includes(searchTerm) ||
            article.author.toLowerCase().includes(searchTerm)
        );
    }

    currentPage = 1;
    renderArticles();
}

// Filter articles by category
function filterArticles() {
    if (currentCategory === 'all') {
        filteredArticles = [...articlesData];
    } else {
        filteredArticles = articlesData.filter(article => article.category === currentCategory);
    }

    // Apply current search if any
    const searchTerm = searchInput.value.trim().toLowerCase();
    if (searchTerm !== '') {
        filteredArticles = filteredArticles.filter(article =>
            article.title.toLowerCase().includes(searchTerm) ||
            article.excerpt.toLowerCase().includes(searchTerm) ||
            article.author.toLowerCase().includes(searchTerm)
        );
    }

    sortArticles();
}

// Sort articles
function sortArticles() {
    switch (currentSort) {
        case 'newest':
            filteredArticles.sort((a, b) => new Date(b.date) - new Date(a.date));
            break;
        case 'oldest':
            filteredArticles.sort((a, b) => new Date(a.date) - new Date(b.date));
            break;
        case 'popular':
            filteredArticles.sort((a, b) => b.views - a.views);
            break;
        case 'title':
            filteredArticles.sort((a, b) => a.title.localeCompare(b.title, 'ar'));
            break;
    }
}

// Render articles
function renderArticles() {
    const startIndex = (currentPage - 1) * articlesPerPage;
    const endIndex = startIndex + articlesPerPage;
    const articlesToShow = filteredArticles.slice(startIndex, endIndex);

    // Clear grid
    articlesGrid.innerHTML = '';

    if (articlesToShow.length === 0) {
        articlesGrid.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>لا توجد مقالات</h3>
                <p>لم يتم العثور على مقالات تطابق البحث أو التصنيف المحدد</p>
            </div>
        `;
        updatePagination();
        return;
    }

    // Render articles
    articlesToShow.forEach(article => {
        const articleElement = createArticleElement(article);
        articlesGrid.appendChild(articleElement);
    });

    updatePagination();
    updateResultsInfo();
}

// Create article element
function createArticleElement(article) {
    const articleDiv = document.createElement('article');
    articleDiv.className = 'article-card';
    articleDiv.dataset.category = article.category;

    const formattedDate = formatDate(article.date);

    articleDiv.innerHTML = `
        <div class="article-image">
            <div class="article-placeholder">
                <i class="${article.icon}"></i>
            </div>
            <div class="article-category">${article.categoryName}</div>
        </div>
        <div class="article-content">
            <h3><a href="#">${article.title}</a></h3>
            <p>${article.excerpt}</p>
            <div class="article-meta">
                <span class="date"><i class="fas fa-calendar"></i> ${formattedDate}</span>
                <span class="author"><i class="fas fa-user"></i> ${article.author}</span>
                <span class="views"><i class="fas fa-eye"></i> ${article.views.toLocaleString()}</span>
            </div>
        </div>
    `;

    return articleDiv;
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('ar-SA', options);
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);

    // Update page info
    currentPageInfo.textContent = currentPage;
    totalPagesInfo.textContent = totalPages;

    // Update prev/next buttons
    prevPage.classList.toggle('disabled', currentPage === 1);
    nextPage.classList.toggle('disabled', currentPage === totalPages);

    // Update page numbers
    pageNumbers.innerHTML = '';

    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }

    pagination.style.display = 'flex';

    // Calculate page range
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    // Add page numbers
    for (let i = startPage; i <= endPage; i++) {
        const pageLink = document.createElement('a');
        pageLink.href = '#';
        pageLink.className = `page-link ${i === currentPage ? 'active' : ''}`;
        pageLink.dataset.page = i;
        pageLink.textContent = i;

        pageLink.addEventListener('click', function(e) {
            e.preventDefault();
            currentPage = parseInt(this.dataset.page);
            renderArticles();
            scrollToTop();
        });

        pageNumbers.appendChild(pageLink);
    }
}

// Update results info
function updateResultsInfo() {
    const startIndex = (currentPage - 1) * articlesPerPage + 1;
    const endIndex = Math.min(currentPage * articlesPerPage, filteredArticles.length);
    const total = filteredArticles.length;

    resultsCount.textContent = `عرض ${startIndex}-${endIndex} من ${total} مقال`;
}

// Scroll to top
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Add loading animation
function showLoading() {
    articlesGrid.innerHTML = '<div class="loading"></div>';
}

// Initialize with sorted articles
sortArticles();
