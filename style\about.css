/* About Page Specific Styles */

/* <PERSON> Header */
.page-header {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 50%, var(--dark-gold) 100%);
    color: white;
    padding: 8rem 0 4rem;
    text-align: center;
    margin-top: 70px;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* About Hero */
.about-hero {
    padding: 6rem 0;
    background: white;
}

.hero-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
}

.lead {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--light-text);
    margin-bottom: 2rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat .number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(212, 175, 55, 0.3);
}

.stat .label {
    color: var(--light-text);
    font-weight: 500;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 6rem;
    opacity: 0.8;
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.3);
}

/* Our Story */
.our-story {
    padding: 6rem 0;
    background: #f8f9fa;
}

.story-text h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 3rem;
    text-align: center;
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    right: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--primary-gold), var(--dark-gold));
    transform: translateX(50%);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-item:nth-child(odd) .timeline-content {
    text-align: left;
    margin-right: 2rem;
}

.timeline-item:nth-child(even) .timeline-content {
    text-align: right;
    margin-left: 2rem;
}

.timeline-year {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
}

.timeline-content {
    flex: 1;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.timeline-content h3 {
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.timeline-content p {
    color: var(--light-text);
    line-height: 1.6;
}

/* Mission & Vision */
.mission-vision {
    padding: 6rem 0;
    background: white;
}

.mv-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.mv-card {
    background: #f8f9fa;
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mv-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.mv-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.mv-card h3 {
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.mv-card p {
    color: var(--light-text);
    line-height: 1.7;
}

/* Why Choose Us */
.why-choose-us {
    padding: 6rem 0;
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

.feature-item h4 {
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.feature-item p {
    color: var(--light-text);
    line-height: 1.6;
}

/* Certifications */
.certifications {
    padding: 6rem 0;
    background: white;
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.cert-item {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.cert-item:hover {
    transform: translateY(-5px);
}

.cert-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
}

.cert-item h4 {
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.cert-item p {
    color: var(--light-text);
    font-size: 0.9rem;
}

/* Technology */
.technology {
    padding: 6rem 0;
    background: #f8f9fa;
}

.tech-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.tech-text h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
}

.tech-text p {
    font-size: 1.1rem;
    color: var(--light-text);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.tech-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.tech-item i {
    color: var(--primary-gold);
    font-size: 1.2rem;
    filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.tech-item span {
    color: var(--dark-text);
    font-weight: 500;
}

.tech-image .image-placeholder {
    width: 400px;
    height: 300px;
}

/* Team Preview */
.team-preview {
    padding: 6rem 0;
    background: white;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.team-member {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-image {
    margin-bottom: 1.5rem;
}

.member-image .image-placeholder {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    font-size: 2.5rem;
}

.member-info h4 {
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.position {
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.experience {
    color: var(--light-text);
    font-size: 0.9rem;
}

.team-cta {
    text-align: center;
}

/* Contact CTA */
.contact-cta {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
}

.cta-buttons .btn-secondary {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.cta-buttons .btn-secondary:hover {
    background: white;
    color: var(--accent-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 6rem 0 3rem;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .timeline::before {
        right: 20px;
        transform: none;
    }

    .timeline-item {
        flex-direction: row !important;
        margin-right: 40px;
    }

    .timeline-item .timeline-content {
        text-align: right !important;
        margin-left: 2rem !important;
        margin-right: 0 !important;
    }

    .tech-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .timeline-item {
        margin-right: 20px;
    }

    .timeline-content {
        padding: 1.5rem;
    }

    .mv-card,
    .feature-item {
        padding: 2rem 1.5rem;
    }

    .image-placeholder {
        width: 250px !important;
        height: 250px !important;
        font-size: 4rem !important;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}
