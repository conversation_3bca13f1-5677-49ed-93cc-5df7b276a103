/* Articles Page Specific Styles */

/* <PERSON> Header */
.page-header {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 50%, var(--dark-gold) 100%);
    color: white;
    padding: 8rem 0 4rem;
    text-align: center;
    margin-top: 70px;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* Articles Section */
.articles-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

/* Filters Bar */
.filters-bar {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 2rem;
    align-items: end;
}

.search-section {
    flex: 1;
}

.filter-section {
    display: flex;
    gap: 2rem;
    align-items: end;
}

.categories-filter {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.categories-filter label {
    font-weight: 600;
    color: var(--accent-blue);
    font-size: 0.95rem;
}

.categories-filter select {
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s ease;
    min-width: 180px;
}

.categories-filter select:focus {
    outline: none;
    border-color: var(--primary-gold);
}

/* Search Box */
.search-box {
    display: flex;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    transition: border-color 0.3s ease;
    max-width: 400px;
}

.search-box:focus-within {
    border-color: var(--primary-gold);
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    outline: none;
    font-family: inherit;
}

.search-box button {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border: none;
    color: white;
    padding: 12px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.search-box button:hover {
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Main Content */
.articles-main {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.articles-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.results-info {
    color: #666;
    font-size: 0.95rem;
}

.sort-options select {
    padding: 8px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.sort-options select:focus {
    outline: none;
    border-color: var(--primary-gold);
}

/* Articles Grid */
.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.article-image {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.article-placeholder {
    font-size: 3rem;
    color: white;
    opacity: 0.7;
}

.article-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, var(--secondary-gold), var(--primary-gold));
    color: var(--accent-blue);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(244, 228, 188, 0.4);
}

.article-content {
    padding: 1.5rem;
}

.article-content h3 {
    margin-bottom: 1rem;
}

.article-content h3 a {
    color: var(--accent-blue);
    text-decoration: none;
    font-size: 1.3rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.article-content h3 a:hover {
    color: var(--primary-gold);
}

.article-content p {
    color: var(--light-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.85rem;
    color: #999;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.article-meta i {
    color: var(--primary-gold);
    filter: drop-shadow(0 1px 2px rgba(212, 175, 55, 0.3));
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 15px;
    color: #666;
    text-decoration: none;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.page-link:hover:not(.disabled) {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    border-color: var(--primary-gold);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.page-link.active {
    background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
    color: white;
    border-color: var(--accent-blue);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.page-link.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.5rem;
}

.pagination-info {
    color: #666;
    font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 6rem 0 3rem;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }

    .filters-bar {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .filter-section {
        justify-content: center;
    }

    .search-box {
        max-width: 100%;
    }

    .articles-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .page-numbers {
        order: -1;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .articles-section {
        padding: 2rem 0;
    }

    .filters-bar,
    .articles-main {
        padding: 1.5rem;
    }

    .article-content {
        padding: 1rem;
    }

    .page-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .categories-filter select {
        min-width: 150px;
    }
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Results */
.no-results {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-results i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-results h3 {
    margin-bottom: 1rem;
    color: #999;
}

/* Filter Active State */
.filter-active {
    background: var(--light-gold) !important;
    border-color: var(--primary-gold) !important;
}
