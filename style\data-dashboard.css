/* لوحة إدارة البيانات - التصميم الذهبي */

/* المتغيرات */
:root {
    --primary-gold: #d4af37;
    --dark-gold: #b8860b;
    --light-gold: #f4e4a6;
    --gold-gradient: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #343a40;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.dashboard-body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--dark-gray);
    line-height: 1.6;
    min-height: 100vh;
}

/* Header */
.dashboard-header {
    background: var(--gold-gradient);
    color: var(--white);
    padding: 1rem 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.logo i {
    font-size: 2rem;
    color: var(--white);
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 600;
    opacity: 0.9;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.user-info i {
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Navigation */
.dashboard-nav {
    background: var(--white);
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 80px;
    z-index: 999;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-tabs {
    display: flex;
    gap: 0;
    overflow-x: auto;
}

.nav-tab {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--gray);
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.nav-tab:hover {
    background: var(--light-gray);
    color: var(--primary-gold);
}

.nav-tab.active {
    color: var(--primary-gold);
    border-bottom-color: var(--primary-gold);
    background: var(--light-gray);
}

.nav-tab i {
    font-size: 1.1rem;
}

/* Main Content */
.dashboard-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Sections */
.dashboard-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.dashboard-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-gold);
}

.section-header h2 {
    color: var(--dark-gray);
    font-size: 2rem;
    font-weight: 700;
}

.section-header p {
    color: var(--gray);
    margin-top: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--gold-gradient);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-outline:hover {
    background: var(--primary-gold);
    color: var(--white);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-danger {
    background: var(--danger);
    color: var(--white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gold-gradient);
}

.stat-card.gold::before {
    background: var(--gold-gradient);
}

.stat-card.blue::before {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.stat-card.green::before {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.stat-card.purple::before {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--light-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: var(--primary-gold);
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
}

.stat-content p {
    color: var(--gray);
    font-weight: 500;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--success);
}

.stat-trend i {
    font-size: 0.8rem;
}

/* Quick Actions */
.quick-actions {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 3rem;
}

.quick-actions h3 {
    color: var(--dark-gray);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.action-btn {
    background: var(--light-gray);
    border: 2px solid transparent;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: right;
}

.action-btn:hover {
    border-color: var(--primary-gold);
    background: var(--light-gold);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.5rem;
    color: var(--primary-gold);
}

.action-btn span {
    font-weight: 600;
    color: var(--dark-gray);
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-header {
    background: var(--gold-gradient);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-icon {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.service-category {
    background: rgba(255,255,255,0.2);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.service-content {
    padding: 2rem;
}

.service-content h3 {
    color: var(--dark-gray);
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.service-details {
    margin-bottom: 2rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray);
}

.detail-item i {
    color: var(--primary-gold);
    width: 20px;
}

.service-actions {
    display: flex;
    gap: 1rem;
    padding: 0 2rem 2rem;
}

/* Doctors Grid */
.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.doctor-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
}

.doctor-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.doctor-avatar {
    width: 80px;
    height: 80px;
    background: var(--light-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.doctor-avatar i {
    font-size: 2rem;
    color: var(--primary-gold);
}

.doctor-info h3 {
    color: var(--dark-gray);
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.specialty {
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.experience {
    color: var(--gray);
    margin-bottom: 1rem;
}

.contact-info {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray);
    font-size: 0.9rem;
}

.contact-item i {
    color: var(--primary-gold);
    width: 16px;
}

.doctor-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Data Table */
.appointments-table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--gold-gradient);
    color: var(--white);
    padding: 1rem;
    text-align: right;
    font-weight: 600;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.data-table tr:hover {
    background: var(--light-gray);
}

.patient-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.patient-info i {
    color: var(--primary-gold);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

/* Patients Stats */
.patients-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.patients-stats .stat-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.patients-stats .stat-item i {
    font-size: 2rem;
    color: var(--primary-gold);
}

.patients-stats .stat-item h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0.25rem;
}

.patients-stats .stat-item p {
    color: var(--gray);
    font-size: 0.9rem;
}

/* Search and Filter */
.patients-search {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-gold);
}

.filter-options select {
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--white);
    cursor: pointer;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.report-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    transition: var(--transition);
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.report-icon {
    width: 60px;
    height: 60px;
    background: var(--light-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.report-icon i {
    font-size: 1.5rem;
    color: var(--primary-gold);
}

.report-content h3 {
    color: var(--dark-gray);
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.report-content p {
    color: var(--gray);
    margin-bottom: 1rem;
}

.report-value {
    margin-bottom: 2rem;
}

.report-value span {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-gold);
    display: block;
}

.report-value small {
    color: var(--gray);
    font-size: 0.9rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gray);
}

.empty-state i {
    font-size: 4rem;
    color: var(--light-gold);
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: var(--dark-gray);
    margin-bottom: 1rem;
}

/* Coming Soon */
.coming-soon {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.coming-soon i {
    font-size: 4rem;
    color: var(--light-gold);
    margin-bottom: 1rem;
}

.coming-soon h3 {
    color: var(--dark-gray);
    margin-bottom: 1rem;
}

.coming-soon p {
    color: var(--gray);
}

/* Alert Messages */
.alert {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    animation: slideDown 0.3s ease-out;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition);
    margin-right: auto;
}

.alert-close:hover {
    background: rgba(0,0,0,0.1);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .logo-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-tabs {
        justify-content: center;
    }

    .nav-tab {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .doctors-grid {
        grid-template-columns: 1fr;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .patients-search {
        flex-direction: column;
        gap: 1rem;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
}
