/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: #f8fafc;
    color: #2d3748;
    line-height: 1.6;
    direction: rtl;
}

/* Header Styles */
.dashboard-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 2rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
}

.logo i {
    font-size: 1.5rem;
    color: #ffd700;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    right: 1rem;
    color: #a0aec0;
}

.search-box input {
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: none;
    border-radius: 25px;
    background: rgba(255,255,255,0.1);
    color: white;
    width: 250px;
    transition: all 0.3s ease;
}

.search-box input::placeholder {
    color: rgba(255,255,255,0.7);
}

.search-box input:focus {
    outline: none;
    background: rgba(255,255,255,0.2);
    width: 300px;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.notification-btn:hover {
    background: rgba(255,255,255,0.1);
}

.notification-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: #e53e3e;
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.user-menu:hover {
    background: rgba(255,255,255,0.1);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2d3748;
    font-size: 1.2rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    color: white;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.8;
}

.user-dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: #f7fafc;
    color: #667eea;
}

.dropdown-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 0.5rem 0;
}

/* Sidebar Styles */
.dashboard-sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    z-index: 999;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.sidebar-nav {
    padding: 2rem 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: #f7fafc;
    color: #667eea;
    border-left-color: #667eea;
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-left-color: #ffd700;
}

.nav-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 2rem;
    left: 0;
    right: 0;
    padding: 0 2rem;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
    padding: 1rem;
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c53030;
}

/* Main Content Styles */
.dashboard-main {
    margin-right: 280px;
    margin-top: 70px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.section-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.section-header p {
    color: #718096;
    margin-top: 0.5rem;
}

/* Welcome Message */
.welcome-message {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    border: 1px solid #c3e6cb;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    animation: slideDown 0.3s ease;
    box-shadow: 0 4px 15px rgba(21, 87, 36, 0.1);
}

.welcome-message i {
    font-size: 1.2rem;
    color: #28a745;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.blogs {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.doctors {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.views {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.comments {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.stat-content p {
    color: #718096;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 20px;
}

.stat-change.positive {
    background: #c6f6d5;
    color: #22543d;
}

.stat-change.negative {
    background: #fed7d7;
    color: #742a2a;
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
}

.chart-period {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    cursor: pointer;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Activity Section */
.activity-section {
    margin-bottom: 2rem;
}

.activity-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.activity-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.activity-item:hover {
    background: #f7fafc;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.activity-icon.blog {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.activity-icon.doctor {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.activity-icon.comment {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.activity-content p {
    margin-bottom: 0.25rem;
    color: #2d3748;
}

.activity-time {
    font-size: 0.8rem;
    color: #718096;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}

.btn-success {
    background: #38a169;
    color: white;
}

.btn-success:hover {
    background: #2f855a;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
}

.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-input,
.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-input {
    min-width: 250px;
}

.filter-select {
    min-width: 150px;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    font-size: 0.9rem;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    color: #4a5568;
}

.data-table tr:hover {
    background: #f7fafc;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-published {
    background: #c6f6d5;
    color: #22543d;
}

.status-draft {
    background: #fef5e7;
    color: #744210;
}

.status-archived {
    background: #e2e8f0;
    color: #4a5568;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn.edit {
    background: #4299e1;
    color: white;
}

.action-btn.edit:hover {
    background: #3182ce;
}

.action-btn.delete {
    background: #e53e3e;
    color: white;
}

.action-btn.delete:hover {
    background: #c53030;
}

.action-btn.view {
    background: #38a169;
    color: white;
}

.action-btn.view:hover {
    background: #2f855a;
}

/* Doctors Grid */
.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.doctor-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.doctor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.doctor-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2.5rem;
    color: white;
}

.doctor-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.doctor-specialty {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.doctor-experience {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.doctor-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #718096;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: #f7fafc;
    color: #2d3748;
}

.modal-body {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-main {
        margin-right: 280px;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .dashboard-sidebar {
        transform: translateX(100%);
    }

    .dashboard-sidebar.active {
        transform: translateX(0);
    }

    .dashboard-main {
        margin-right: 0;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .doctors-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }

    .search-box input {
        width: 200px;
    }

    .search-box input:focus {
        width: 220px;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-input,
    .filter-select {
        min-width: auto;
    }

    .table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }

    .doctors-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.category-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.category-info {
    flex: 1;
}

.category-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.category-info p {
    color: #718096;
    font-size: 0.9rem;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Advanced Stats */
.advanced-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-icon.analytics {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.time {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.bounce {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.share {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Analytics Charts */
.analytics-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-card.large {
    grid-column: span 1;
}

.chart-legend {
    display: flex;
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #4a5568;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Top Blogs List */
.top-blogs-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.top-blog-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    background: #f7fafc;
    transition: background 0.3s ease;
}

.top-blog-item:hover {
    background: #edf2f7;
}

.blog-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.blog-info h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.blog-info p {
    font-size: 0.8rem;
    color: #718096;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
}

.settings-header i {
    font-size: 1.2rem;
    opacity: 0.8;
}

.settings-content {
    padding: 2rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
}

.setting-item input,
.setting-item textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.setting-item input:focus,
.setting-item textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
    margin-bottom: 0 !important;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #667eea;
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Analytics Controls */
.analytics-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Additional Responsive Styles */
@media (max-width: 1200px) {
    .analytics-charts {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .settings-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }

    .category-card {
        flex-direction: column;
        text-align: center;
    }

    .advanced-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .analytics-controls {
        flex-direction: column;
        align-items: stretch;
    }

    /* Services Management Responsive */
    .services-management-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-management-card {
        margin-bottom: 1rem;
    }

    .service-card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .service-card-body {
        padding: 1.5rem;
    }

    .service-card-actions {
        padding: 1rem 1.5rem 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .service-card-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .service-stats {
        flex-direction: column;
        gap: 0.75rem;
    }
}

/* Services Management Styles */
.services-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-management-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e5e7eb;
}

.service-management-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.service-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.service-icon {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.service-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.service-status.active {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.service-status.inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #fee2e2;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.service-card-body {
    padding: 2rem;
}

.service-card-body h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.service-category,
.service-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #6b7280;
    font-size: 0.95rem;
}

.service-category i,
.service-price i {
    color: #667eea;
    width: 16px;
}

.service-price {
    font-weight: 600;
    color: #059669;
}

.service-stats {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.service-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.service-stats .stat i {
    color: #667eea;
    width: 14px;
}

.service-card-actions {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.toggle-switch input[type="checkbox"] {
    position: relative;
    width: 50px;
    height: 25px;
    appearance: none;
    background: #d1d5db;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked {
    background: #667eea;
}

.toggle-switch input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-switch input[type="checkbox"]:checked::before {
    transform: translateX(25px);
}

.toggle-label {
    font-weight: 500;
    color: #374151;
}
