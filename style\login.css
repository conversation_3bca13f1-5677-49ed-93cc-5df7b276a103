* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #D4AF37 0%, #F4E4BC 50%, #D4AF37 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Back to Home Button */
.back-home-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    color: #D4AF37;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
}

.back-home-btn:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.back-home-btn i {
    font-size: 1rem;
}

.container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.3);
    border: 1px solid rgba(212, 175, 55, 0.2);
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.logo-section i {
    font-size: 2.5rem;
    color: #D4AF37;
    filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.login-header h1 {
    color: #D4AF37;
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
}

.login-header p {
    color: #666;
    font-size: 0.95rem;
    font-weight: 400;
}

/* Error Message */
.error-message {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 12px 15px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    animation: slideDown 0.3s ease;
}

.error-message i {
    font-size: 1rem;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    color: #333;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.input-group input {
    width: 100%;
    padding: 15px 50px 15px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.input-group input:focus {
    outline: none;
    border-color: #D4AF37;
    background: white;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    color: #D4AF37;
    pointer-events: none;
    transition: all 0.3s ease;
}

.input-group:focus-within .input-icon,
.input-group.focused .input-icon {
    color: #B8860B;
    transform: translateY(-50%) scale(1.1);
}

.input-group.focused input {
    border-color: #D4AF37;
    background: white;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #666;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #D4AF37;
    border-radius: 4px;
    margin-left: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #D4AF37;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.forgot-password {
    color: #D4AF37;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #B8941F;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #D4AF37, #F4E4BC);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.signup-link {
    text-align: center;
    margin-top: 25px;
    color: #666;
    font-size: 0.9rem;
}

.signup-link a {
    color: #D4AF37;
    text-decoration: none;
    font-weight: 600;
}

.signup-link a:hover {
    text-decoration: underline;
}

.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 10%;
    animation-delay: 2s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    top: 60%;
    right: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 15px;
    }

    .login-card {
        padding: 30px 20px;
    }

    .login-header h1 {
        font-size: 1.8rem;
    }

    .logo-section {
        flex-direction: column;
        gap: 10px;
    }

    .logo-section i {
        font-size: 2rem;
    }

    .form-options {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .back-home-btn {
        top: 15px;
        right: 15px;
        padding: 10px 15px;
        font-size: 0.8rem;
    }

    .back-home-btn span {
        display: none;
    }
}
