/* Services Page Specific Styles */

/* <PERSON> Header */
.page-header {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 50%, var(--dark-gold) 100%);
    color: white;
    padding: 8rem 0 4rem;
    text-align: center;
    margin-top: 70px;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* Services Overview */
.services-overview {
    padding: 4rem 0;
    background: white;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

.stat-content h3 {
    font-size: 2rem;
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
}

.stat-content p {
    color: var(--light-text);
    font-weight: 500;
}

/* Services Categories */
.services-categories {
    padding: 3rem 0;
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--light-text);
}

.categories-nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.category-btn {
    padding: 12px 24px;
    border: 2px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-family: inherit;
}

.category-btn:hover,
.category-btn.active {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    border-color: var(--primary-gold);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

/* Services Grid */
.services-grid-section {
    padding: 4rem 0;
    background: white;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.no-services-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.no-services-message i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #d4af37;
}

.no-services-message h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.no-services-message p {
    font-size: 1rem;
    opacity: 0.8;
}

.service-detail-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #f0f0f0;
}

.service-detail-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-header {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    padding: 2rem;
    color: white;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
}

.service-badge {
    background: linear-gradient(135deg, var(--secondary-gold), var(--primary-gold));
    color: var(--accent-blue);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(244, 228, 188, 0.4);
}

.service-content {
    padding: 2rem;
}

.service-content h3 {
    font-size: 1.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--light-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    margin-bottom: 1.5rem;
}

.service-features h4 {
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-features ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--light-text);
}

.service-features li i {
    color: var(--primary-gold);
    font-size: 0.9rem;
    filter: drop-shadow(0 1px 2px rgba(212, 175, 55, 0.3));
}

.service-info {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--light-text);
    font-size: 0.9rem;
}

.info-item i {
    color: var(--primary-gold);
    filter: drop-shadow(0 1px 2px rgba(212, 175, 55, 0.3));
}

.service-price {
    margin-bottom: 1.5rem;
}

.service-price .price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--accent-blue);
}

.book-btn {
    width: 100%;
    padding: 12px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.book-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

/* CTA Section */
.cta-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
}

.cta-buttons .btn-secondary {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.cta-buttons .btn-secondary:hover {
    background: white;
    color: var(--accent-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 6rem 0 3rem;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }

    .overview-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .stat-item {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }

    .categories-nav {
        justify-content: center;
    }

    .category-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .service-header {
        padding: 1.5rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .service-content {
        padding: 1.5rem;
    }

    .service-info {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .services-grid {
        grid-template-columns: 1fr;
    }

    .service-detail-card {
        margin: 0 10px;
    }

    .service-content {
        padding: 1rem;
    }

    .service-header {
        padding: 1rem;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Animation for filtered services */
.service-detail-card.hidden {
    display: none;
}

.service-detail-card.show {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
