// About Page JavaScript

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    animateCounters();
    setupScrollAnimations();
    setupTimelineAnimations();
    setupHoverEffects();
});

// Animate counter numbers
function animateCounters() {
    const counters = document.querySelectorAll('.stat .number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.dataset.target);
                animateNumber(counter, 0, target, 2000);
                observer.unobserve(counter);
            }
        });
    }, {
        threshold: 0.5
    });
    
    counters.forEach(counter => observer.observe(counter));
}

// Animate number counting
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function (ease out cubic)
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOutCubic);
        
        element.textContent = current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = end.toLocaleString();
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// Setup scroll animations
function setupScrollAnimations() {
    const animatedElements = document.querySelectorAll(
        '.mv-card, .feature-item, .cert-item, .tech-item, .team-member'
    );
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
}

// Setup timeline animations
function setupTimelineAnimations() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.3
    });
    
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(50px)';
        item.style.transition = `opacity 0.6s ease ${index * 0.2}s, transform 0.6s ease ${index * 0.2}s`;
        observer.observe(item);
    });
}

// Setup hover effects
function setupHoverEffects() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.mv-card, .feature-item, .cert-item, .team-member');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255,255,255,0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Add ripple animation styles
const rippleStyles = document.createElement('style');
rippleStyles.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyles);

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const heroImage = document.querySelector('.hero-image .image-placeholder');
    
    if (heroImage) {
        heroImage.style.transform = `translateY(${scrolled * 0.1}px) rotate(${scrolled * 0.05}deg)`;
    }
});

// Add smooth scrolling for internal links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading animation for images
function addImageLoadingEffect() {
    const imagePlaceholders = document.querySelectorAll('.image-placeholder');
    
    imagePlaceholders.forEach(placeholder => {
        placeholder.style.animation = 'pulse 2s infinite';
    });
    
    const pulseStyles = document.createElement('style');
    pulseStyles.textContent = `
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }
    `;
    document.head.appendChild(pulseStyles);
}

// Initialize image loading effects
addImageLoadingEffect();

// Add intersection observer for tech items
function setupTechItemsAnimation() {
    const techItems = document.querySelectorAll('.tech-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateX(0)';
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });
    
    techItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-30px)';
        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        observer.observe(item);
    });
}

// Initialize tech items animation
setupTechItemsAnimation();

// Add typing effect for hero title
function addTypingEffect() {
    const heroTitle = document.querySelector('.hero-text h2');
    if (heroTitle) {
        const text = heroTitle.textContent;
        heroTitle.textContent = '';
        heroTitle.style.borderLeft = '2px solid #4CAF50';
        heroTitle.style.paddingLeft = '5px';
        
        let index = 0;
        const typeInterval = setInterval(() => {
            heroTitle.textContent += text[index];
            index++;
            
            if (index >= text.length) {
                clearInterval(typeInterval);
                setTimeout(() => {
                    heroTitle.style.borderLeft = 'none';
                }, 500);
            }
        }, 100);
    }
}

// Initialize typing effect after a delay
setTimeout(addTypingEffect, 1000);

// Add progress bar for timeline
function addTimelineProgress() {
    const timeline = document.querySelector('.timeline');
    if (timeline) {
        const progressBar = document.createElement('div');
        progressBar.className = 'timeline-progress';
        progressBar.style.cssText = `
            position: absolute;
            right: 50%;
            top: 0;
            width: 4px;
            background: #4CAF50;
            transform: translateX(50%);
            height: 0%;
            transition: height 0.3s ease;
            z-index: 1;
        `;
        timeline.appendChild(progressBar);
        
        window.addEventListener('scroll', () => {
            const timelineRect = timeline.getBoundingClientRect();
            const windowHeight = window.innerHeight;
            
            if (timelineRect.top < windowHeight && timelineRect.bottom > 0) {
                const progress = Math.min(
                    Math.max((windowHeight - timelineRect.top) / timelineRect.height, 0),
                    1
                );
                progressBar.style.height = `${progress * 100}%`;
            }
        });
    }
}

// Initialize timeline progress
addTimelineProgress();

// Add floating animation to icons
function addFloatingAnimation() {
    const icons = document.querySelectorAll('.mv-icon, .feature-icon, .cert-icon');
    
    icons.forEach((icon, index) => {
        icon.style.animation = `float 3s ease-in-out infinite ${index * 0.5}s`;
    });
    
    const floatStyles = document.createElement('style');
    floatStyles.textContent = `
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
    `;
    document.head.appendChild(floatStyles);
}

// Initialize floating animation
addFloatingAnimation();

// Add click tracking for analytics (placeholder)
function trackClicks() {
    const trackableElements = document.querySelectorAll('.btn, .team-member, .mv-card');
    
    trackableElements.forEach(element => {
        element.addEventListener('click', function() {
            // Placeholder for analytics tracking
            console.log('Element clicked:', this.className);
        });
    });
}

// Initialize click tracking
trackClicks();
