<?php
// اختبار شامل للوحة التحكم المكتملة
require_once 'config/database.php';

echo "<h1>🧪 اختبار شامل للوحة التحكم</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ فشل الاتصال: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
}

// اختبار الجداول والبيانات
echo "<h2>2. اختبار الجداول والبيانات</h2>";
$tables = [
    'users_d' => 'المستخدمين',
    'categories' => 'التصنيفات',
    'articles' => 'المقالات',
    'doctors' => 'الأطباء',
    'services' => 'الخدمات'
];

$allTablesOk = true;
foreach ($tables as $table => $arabicName) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $count_result->fetch_assoc()['count'];
        echo "<p style='color: green;'>✅ جدول $arabicName ($table): $count صف</p>";
        
        if ($count == 0) {
            echo "<p style='color: orange; margin-right: 20px;'>⚠️ الجدول فارغ</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ جدول $arabicName ($table) غير موجود</p>";
        $allTablesOk = false;
    }
}

// اختبار ملفات النظام
echo "<h2>3. اختبار ملفات النظام</h2>";
$files = [
    'admin-dashboard.php' => 'الصفحة الرئيسية',
    'js/admin-dashboard.js' => 'ملف JavaScript',
    'php/dashboard-data.php' => 'ملف جلب البيانات',
    'php/dashboard-actions.php' => 'ملف العمليات',
    'style/admin-dashboard.css' => 'ملف التصميم'
];

$allFilesOk = true;
foreach ($files as $file => $description) {
    if (file_exists($file)) {
        $size = round(filesize($file) / 1024, 2);
        echo "<p style='color: green;'>✅ $description ($file): ${size}KB</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file) غير موجود</p>";
        $allFilesOk = false;
    }
}

// اختبار وظائف قاعدة البيانات
echo "<h2>4. اختبار وظائف قاعدة البيانات</h2>";

// اختبار جلب الإحصائيات
try {
    $stats_query = "SELECT 
        (SELECT COUNT(*) FROM articles) as articles,
        (SELECT COUNT(*) FROM categories) as categories,
        (SELECT COUNT(*) FROM doctors) as doctors,
        (SELECT COUNT(*) FROM services) as services,
        (SELECT SUM(views) FROM articles) as total_views";
    
    $stats_result = $conn->query($stats_query);
    if ($stats_result) {
        $stats = $stats_result->fetch_assoc();
        echo "<p style='color: green;'>✅ جلب الإحصائيات: نجح</p>";
        echo "<div style='margin-right: 20px; background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
        echo "<p>📊 المقالات: " . $stats['articles'] . "</p>";
        echo "<p>🏷️ التصنيفات: " . $stats['categories'] . "</p>";
        echo "<p>👨‍⚕️ الأطباء: " . $stats['doctors'] . "</p>";
        echo "<p>🛠️ الخدمات: " . $stats['services'] . "</p>";
        echo "<p>👁️ إجمالي المشاهدات: " . number_format($stats['total_views'] ?? 0) . "</p>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ جلب الإحصائيات: فشل</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
}

// اختبار العلاقات بين الجداول
echo "<h2>5. اختبار العلاقات بين الجداول</h2>";

try {
    // اختبار علاقة المقالات بالتصنيفات
    $articles_categories = $conn->query("
        SELECT a.title, c.name as category_name 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LIMIT 3
    ");
    
    if ($articles_categories && $articles_categories->num_rows > 0) {
        echo "<p style='color: green;'>✅ علاقة المقالات بالتصنيفات: تعمل</p>";
        while ($row = $articles_categories->fetch_assoc()) {
            echo "<p style='margin-right: 20px;'>📝 " . $row['title'] . " - " . ($row['category_name'] ?? 'بدون تصنيف') . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد مقالات مرتبطة بتصنيفات</p>";
    }
    
    // اختبار علاقة المقالات بالمؤلفين
    $articles_authors = $conn->query("
        SELECT a.title, u.name as author_name 
        FROM articles a 
        LEFT JOIN users u ON a.author_id = u.id 
        LIMIT 3
    ");
    
    if ($articles_authors && $articles_authors->num_rows > 0) {
        echo "<p style='color: green;'>✅ علاقة المقالات بالمؤلفين: تعمل</p>";
        while ($row = $articles_authors->fetch_assoc()) {
            echo "<p style='margin-right: 20px;'>✍️ " . $row['title'] . " - " . ($row['author_name'] ?? 'مؤلف غير معروف') . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد مقالات مرتبطة بمؤلفين</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار العلاقات: " . $e->getMessage() . "</p>";
}

// اختبار وظائف البحث
echo "<h2>6. اختبار وظائف البحث</h2>";

try {
    // البحث في المقالات
    $search_articles = $conn->query("SELECT title FROM articles WHERE title LIKE '%أسنان%' LIMIT 3");
    if ($search_articles) {
        echo "<p style='color: green;'>✅ البحث في المقالات: يعمل (" . $search_articles->num_rows . " نتيجة)</p>";
    }
    
    // البحث في الأطباء
    $search_doctors = $conn->query("SELECT name FROM doctors WHERE name LIKE '%د.%' LIMIT 3");
    if ($search_doctors) {
        echo "<p style='color: green;'>✅ البحث في الأطباء: يعمل (" . $search_doctors->num_rows . " نتيجة)</p>";
    }
    
    // البحث في الخدمات
    $search_services = $conn->query("SELECT name FROM services WHERE name LIKE '%تنظيف%' LIMIT 3");
    if ($search_services) {
        echo "<p style='color: green;'>✅ البحث في الخدمات: يعمل (" . $search_services->num_rows . " نتيجة)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار البحث: " . $e->getMessage() . "</p>";
}

// اختبار صحة البيانات
echo "<h2>7. اختبار صحة البيانات</h2>";

try {
    // التحقق من وجود مقالات بدون تصنيف
    $orphan_articles = $conn->query("SELECT COUNT(*) as count FROM articles WHERE category_id NOT IN (SELECT id FROM categories)");
    $orphan_count = $orphan_articles->fetch_assoc()['count'];
    
    if ($orphan_count == 0) {
        echo "<p style='color: green;'>✅ جميع المقالات مرتبطة بتصنيفات صحيحة</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ يوجد $orphan_count مقال غير مرتبط بتصنيف صحيح</p>";
    }
    
    // التحقق من وجود مقالات بدون مؤلف
    $authorless_articles = $conn->query("SELECT COUNT(*) as count FROM articles WHERE author_id NOT IN (SELECT id FROM users)");
    $authorless_count = $authorless_articles->fetch_assoc()['count'];
    
    if ($authorless_count == 0) {
        echo "<p style='color: green;'>✅ جميع المقالات مرتبطة بمؤلفين صحيحين</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ يوجد $authorless_count مقال غير مرتبط بمؤلف صحيح</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار صحة البيانات: " . $e->getMessage() . "</p>";
}

// النتيجة النهائية
echo "<h2>8. النتيجة النهائية</h2>";

if ($allTablesOk && $allFilesOk) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 تهانينا! النظام جاهز للاستخدام</h3>";
    echo "<p>جميع الاختبارات نجحت. يمكنك الآن استخدام لوحة التحكم بثقة.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ يوجد مشاكل تحتاج إلى إصلاح</h3>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل الاستخدام.</p>";
    echo "</div>";
}

// روابط مفيدة
echo "<h2>9. روابط مفيدة</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px;'>";
echo "<p><a href='fix-database.php' style='color: #0066cc; font-weight: bold;'>🔧 إصلاح قاعدة البيانات</a></p>";
echo "<p><a href='admin-dashboard.php' style='color: #d4af37; font-weight: bold;'>🎯 لوحة التحكم</a></p>";
echo "<p><a href='signin.php' style='color: #28a745; font-weight: bold;'>🔐 تسجيل الدخول</a></p>";
echo "</div>";

echo "<h2>10. معلومات تسجيل الدخول</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7;'>";
echo "<h4>👤 حسابات المستخدمين:</h4>";
echo "<p><strong>المدير:</strong> admin / admin123</p>";
echo "<p><strong>الطبيب:</strong> doctor1 / doctor123</p>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

h1 {
    color: #d4af37;
    border-bottom: 3px solid #d4af37;
    padding-bottom: 15px;
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

h2 {
    color: #2c3e50;
    margin-top: 30px;
    border-right: 4px solid #d4af37;
    padding-right: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

p {
    margin: 8px 0;
    line-height: 1.6;
}

a {
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
