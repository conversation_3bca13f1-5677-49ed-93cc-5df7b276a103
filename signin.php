<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - سمايل ديزاين لطب الأسنان</title>
    <link rel="stylesheet" href="style/login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Back to Home Button -->
    <a href="index.php" class="back-home-btn" title="العودة للصفحة الرئيسية">
        <i class="fas fa-arrow-right"></i>
        <span>العودة للرئيسية</span>
    </a>

    <div class="container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-section">
                    <i class="fas fa-tooth"></i>
                    <h1>تسجيل الدخول</h1>
                </div>
                <p>مرحباً بك في سمايل ديزاين لطب الأسنان</p>

                <?php if (isset($_GET['error'])): ?>
                    <div class="error-message">
                        <?php
                        switch ($_GET['error']) {
                            case 'invalid_password':
                                echo '<i class="fas fa-exclamation-triangle"></i> كلمة المرور غير صحيحة';
                                break;
                            case 'user_not_found':
                                echo '<i class="fas fa-user-times"></i> اسم المستخدم غير موجود';
                                break;
                            case 'missing_data':
                                echo '<i class="fas fa-exclamation-circle"></i> يرجى ملء جميع الحقول';
                                break;
                            default:
                                echo '<i class="fas fa-exclamation-triangle"></i> حدث خطأ، يرجى المحاولة مرة أخرى';
                        }
                        ?>
                    </div>
                <?php endif; ?>
            </div>

            <form class="login-form" id="loginForm" method="POST" action="login.php">
                <div class="input-group">
                    <label for="username">اسم المستخدم أو البريد الإلكتروني</label>
                    <input type="text" id="username" name="username" required>
                    <span class="input-icon"><i class="fas fa-user"></i></span>
                </div>

                <div class="input-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                    <span class="input-icon"><i class="fas fa-lock"></i></span>
                </div>

                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="remember">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="login-btn">تسجيل الدخول</button>

                <div class="signup-link">
                    <p>ليس لديك حساب؟ <a href="#">إنشاء حساب جديد</a></p>
                </div>
            </form>
        </div>

        <div class="background-decoration">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>
    </div>

    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const inputs = form.querySelectorAll('input[required]');

            // إضافة تأثيرات بصرية للحقول
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // إذا كان الحقل يحتوي على قيمة عند التحميل
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }
            });

            // تحسين إرسال النموذج
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
                submitBtn.disabled = true;

                // السماح بإرسال النموذج
                return true;
            });
        });

        // إضافة تأثير النقر للأزرار
        document.querySelectorAll('.login-btn, .back-home-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                // إضافة تأثير النقر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
