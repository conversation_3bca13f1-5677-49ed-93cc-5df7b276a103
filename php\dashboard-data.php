<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// الحصول على نوع العملية
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'stats':
            getStats();
            break;
        case 'recent_articles':
            getRecentArticles();
            break;
        case 'recent_doctors':
            getRecentDoctors();
            break;
        case 'categories':
            getCategories();
            break;
        case 'articles':
            getArticles();
            break;
        case 'doctors':
            getDoctors();
            break;
        case 'services':
            getServices();
            break;
        case 'categories_chart':
            getCategoriesChart();
            break;
        case 'views_chart':
            getViewsChart();
            break;
        case 'top_articles':
            getTopArticles();
            break;
        case 'content_growth':
            getContentGrowth();
            break;
        case 'get_article':
            getArticle();
            break;
        case 'get_category':
            getCategory();
            break;
        case 'get_doctor':
            getDoctor();
            break;
        case 'get_service':
            getService();
            break;
        case 'search_data':
            searchData();
            break;
        case 'dashboard_summary':
            getDashboardSummary();
            break;
        case 'articles_advanced':
            getArticlesAdvanced();
            break;
        case 'authors':
            getAuthors();
            break;
        case 'top_articles':
            getTopArticles();
            break;
        case 'content_growth':
            getContentGrowth();
            break;
        case 'categories_chart':
            getCategoriesChart();
            break;
        case 'views_chart':
            getViewsChart();
            break;
        case 'analytics_summary':
            getAnalyticsSummary();
            break;
        case 'daily_views':
            getDailyViews();
            break;
        case 'top_articles_table':
            getTopArticlesTable();
            break;
        case 'categories_stats':
            getCategoriesStats();
            break;
        default:
            throw new Exception('عملية غير صحيحة');
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// جلب الإحصائيات العامة
function getStats() {
    global $conn;
    
    $stats = [];
    
    // عدد المقالات
    $result = $conn->query("SELECT COUNT(*) as total FROM articles");
    $stats['articles'] = $result->fetch_assoc()['total'];
    
    // عدد التصنيفات
    $result = $conn->query("SELECT COUNT(*) as total FROM categories");
    $stats['categories'] = $result->fetch_assoc()['total'];
    
    // عدد الأطباء
    $result = $conn->query("SELECT COUNT(*) as total FROM doctors");
    $stats['doctors'] = $result->fetch_assoc()['total'];
    
    // عدد الخدمات
    $result = $conn->query("SELECT COUNT(*) as total FROM services");
    $stats['services'] = $result->fetch_assoc()['total'];
    
    // إجمالي المشاهدات
    $result = $conn->query("SELECT SUM(views) as total FROM articles");
    $stats['views'] = $result->fetch_assoc()['total'] ?? 0;
    
    echo json_encode(['success' => true, 'stats' => $stats]);
}

// جلب المقالات الحديثة
function getRecentArticles() {
    global $conn;
    
    $query = "SELECT a.id, a.title, a.created_at, c.name as category_name 
              FROM articles a 
              LEFT JOIN categories c ON a.category_id = c.id 
              ORDER BY a.created_at DESC 
              LIMIT 5";
    
    $result = $conn->query($query);
    $articles = [];
    
    while ($row = $result->fetch_assoc()) {
        $articles[] = $row;
    }
    
    echo json_encode(['success' => true, 'articles' => $articles]);
}

// جلب الأطباء الحديثين
function getRecentDoctors() {
    global $conn;
    
    $query = "SELECT id, name, specialty, experience, created_at 
              FROM doctors 
              ORDER BY created_at DESC 
              LIMIT 5";
    
    $result = $conn->query($query);
    $doctors = [];
    
    while ($row = $result->fetch_assoc()) {
        $doctors[] = $row;
    }
    
    echo json_encode(['success' => true, 'doctors' => $doctors]);
}

// جلب التصنيفات
function getCategories() {
    global $conn;
    
    $query = "SELECT c.*, COUNT(a.id) as articles_count 
              FROM categories c 
              LEFT JOIN articles a ON c.id = a.category_id 
              GROUP BY c.id 
              ORDER BY c.name";
    
    $result = $conn->query($query);
    $categories = [];
    
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
    
    echo json_encode(['success' => true, 'categories' => $categories]);
}

// جلب المقالات مع الفلترة
function getArticles() {
    global $conn;
    
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    $status = $_GET['status'] ?? '';
    
    $query = "SELECT a.*, c.name as category_name, u.name as author_name
              FROM articles a
              LEFT JOIN categories c ON a.category_id = c.id
              LEFT JOIN `users_d` u ON a.author_id = u.id
              WHERE 1=1";
    
    $params = [];
    $types = '';
    
    if (!empty($search)) {
        $query .= " AND (a.title LIKE ? OR a.content LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $types .= 'ss';
    }
    
    if (!empty($category)) {
        $query .= " AND a.category_id = ?";
        $params[] = $category;
        $types .= 'i';
    }
    
    if (!empty($status)) {
        $query .= " AND a.status = ?";
        $params[] = $status;
        $types .= 's';
    }
    
    $query .= " ORDER BY a.created_at DESC";
    
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $articles = [];
    while ($row = $result->fetch_assoc()) {
        $articles[] = $row;
    }
    
    echo json_encode(['success' => true, 'articles' => $articles]);
}

// جلب الأطباء
function getDoctors() {
    global $conn;
    
    $query = "SELECT * FROM doctors ORDER BY created_at DESC";
    $result = $conn->query($query);
    
    $doctors = [];
    while ($row = $result->fetch_assoc()) {
        $doctors[] = $row;
    }
    
    echo json_encode(['success' => true, 'doctors' => $doctors]);
}

// جلب الخدمات
function getServices() {
    global $conn;
    
    $query = "SELECT * FROM services ORDER BY created_at DESC";
    $result = $conn->query($query);
    
    $services = [];
    while ($row = $result->fetch_assoc()) {
        $services[] = $row;
    }
    
    echo json_encode(['success' => true, 'services' => $services]);
}

// جلب بيانات مخطط التصنيفات
function getCategoriesChart() {
    global $conn;
    
    $query = "SELECT c.name, COUNT(a.id) as count 
              FROM categories c 
              LEFT JOIN articles a ON c.id = a.category_id 
              GROUP BY c.id, c.name 
              HAVING count > 0 
              ORDER BY count DESC";
    
    $result = $conn->query($query);
    
    $labels = [];
    $data = [];
    
    while ($row = $result->fetch_assoc()) {
        $labels[] = $row['name'];
        $data[] = (int)$row['count'];
    }
    
    $chartData = [
        'labels' => $labels,
        'data' => $data
    ];
    
    echo json_encode(['success' => true, 'chart_data' => $chartData]);
}

// جلب بيانات مخطط المشاهدات
function getViewsChart() {
    global $conn;
    
    // جلب المشاهدات لآخر 6 أشهر
    $query = "SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                SUM(views) as total_views
              FROM articles 
              WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
              GROUP BY DATE_FORMAT(created_at, '%Y-%m')
              ORDER BY month";
    
    $result = $conn->query($query);
    
    $labels = [];
    $data = [];
    
    while ($row = $result->fetch_assoc()) {
        $monthName = getArabicMonth($row['month']);
        $labels[] = $monthName;
        $data[] = (int)$row['total_views'];
    }
    
    // إذا لم توجد بيانات، أنشئ بيانات وهمية
    if (empty($labels)) {
        $labels = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
        $data = [1200, 1900, 3000, 5000, 2000, 3000];
    }
    
    $chartData = [
        'labels' => $labels,
        'data' => $data
    ];
    
    echo json_encode(['success' => true, 'chart_data' => $chartData]);
}

// جلب أكثر المقالات مشاهدة
function getTopArticles() {
    global $conn;

    $query = "SELECT title, views
              FROM articles
              WHERE views > 0
              ORDER BY views DESC
              LIMIT 10";

    $result = $conn->query($query);

    $labels = [];
    $data = [];

    while ($row = $result->fetch_assoc()) {
        $labels[] = mb_substr($row['title'], 0, 30) . '...';
        $data[] = (int)$row['views'];
    }

    // إذا لم توجد بيانات، أنشئ بيانات وهمية
    if (empty($labels)) {
        $labels = ['مقال تجريبي 1', 'مقال تجريبي 2', 'مقال تجريبي 3'];
        $data = [1500, 1200, 800];
    }

    $chartData = [
        'labels' => $labels,
        'data' => $data
    ];

    echo json_encode(['success' => true, 'chart_data' => $chartData]);
}

// جلب نمو المحتوى الشهري
function getContentGrowth() {
    global $conn;

    $query = "SELECT
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as articles_count
              FROM articles
              WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
              GROUP BY DATE_FORMAT(created_at, '%Y-%m')
              ORDER BY month";

    $result = $conn->query($query);

    $labels = [];
    $data = [];

    while ($row = $result->fetch_assoc()) {
        $monthName = getArabicMonth($row['month']);
        $labels[] = $monthName;
        $data[] = (int)$row['articles_count'];
    }

    // إذا لم توجد بيانات، أنشئ بيانات وهمية
    if (empty($labels)) {
        $labels = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
        $data = [5, 8, 12, 15, 10, 18];
    }

    $chartData = [
        'labels' => $labels,
        'data' => $data
    ];

    echo json_encode(['success' => true, 'chart_data' => $chartData]);
}

// تحويل الشهر إلى العربية
function getArabicMonth($yearMonth) {
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس',
        '04' => 'أبريل', '05' => 'مايو', '06' => 'يونيو',
        '07' => 'يوليو', '08' => 'أغسطس', '09' => 'سبتمبر',
        '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];

    $parts = explode('-', $yearMonth);
    $month = $parts[1] ?? '01';

    return $months[$month] ?? 'غير معروف';
}

// جلب مقال واحد للتعديل
function getArticle() {
    global $conn;

    $id = (int)($_GET['id'] ?? 0);

    if ($id <= 0) {
        throw new Exception('معرف المقال غير صحيح');
    }

    $query = "SELECT * FROM articles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('المقال غير موجود');
    }

    $article = $result->fetch_assoc();
    echo json_encode(['success' => true, 'article' => $article]);
}

// جلب تصنيف واحد للتعديل
function getCategory() {
    global $conn;

    $id = (int)($_GET['id'] ?? 0);

    if ($id <= 0) {
        throw new Exception('معرف التصنيف غير صحيح');
    }

    $query = "SELECT * FROM categories WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('التصنيف غير موجود');
    }

    $category = $result->fetch_assoc();
    echo json_encode(['success' => true, 'category' => $category]);
}

// جلب طبيب واحد للتعديل
function getDoctor() {
    global $conn;

    $id = (int)($_GET['id'] ?? 0);

    if ($id <= 0) {
        throw new Exception('معرف الطبيب غير صحيح');
    }

    $query = "SELECT * FROM doctors WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('الطبيب غير موجود');
    }

    $doctor = $result->fetch_assoc();
    echo json_encode(['success' => true, 'doctor' => $doctor]);
}

// جلب خدمة واحدة للتعديل
function getService() {
    global $conn;

    $id = (int)($_GET['id'] ?? 0);

    if ($id <= 0) {
        throw new Exception('معرف الخدمة غير صحيح');
    }

    $query = "SELECT * FROM services WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('الخدمة غير موجودة');
    }

    $service = $result->fetch_assoc();
    echo json_encode(['success' => true, 'service' => $service]);
}

// البحث العام في البيانات
function searchData() {
    global $conn;

    $query = $_GET['query'] ?? '';
    $type = $_GET['type'] ?? 'all';

    if (empty($query)) {
        throw new Exception('نص البحث مطلوب');
    }

    $results = [];
    $searchParam = "%$query%";

    if ($type === 'all' || $type === 'articles') {
        $stmt = $conn->prepare("SELECT id, title, 'article' as type FROM articles WHERE title LIKE ? OR content LIKE ? LIMIT 10");
        $stmt->bind_param('ss', $searchParam, $searchParam);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $results[] = $row;
        }
    }

    if ($type === 'all' || $type === 'doctors') {
        $stmt = $conn->prepare("SELECT id, name as title, 'doctor' as type FROM doctors WHERE name LIKE ? OR specialty LIKE ? LIMIT 10");
        $stmt->bind_param('ss', $searchParam, $searchParam);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $results[] = $row;
        }
    }

    if ($type === 'all' || $type === 'services') {
        $stmt = $conn->prepare("SELECT id, name as title, 'service' as type FROM services WHERE name LIKE ? OR description LIKE ? LIMIT 10");
        $stmt->bind_param('ss', $searchParam, $searchParam);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $results[] = $row;
        }
    }

    echo json_encode(['success' => true, 'results' => $results]);
}

// ملخص لوحة التحكم المتقدم
function getDashboardSummary() {
    global $conn;

    $summary = [];

    // إحصائيات اليوم
    $today = date('Y-m-d');

    // المقالات المنشورة اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM articles WHERE DATE(created_at) = ? AND status = 'published'");
    $stmt->bind_param('s', $today);
    $stmt->execute();
    $summary['articles_today'] = $stmt->get_result()->fetch_assoc()['count'];

    // إجمالي المشاهدات هذا الشهر
    $thisMonth = date('Y-m');
    $stmt = $conn->prepare("SELECT SUM(views) as total FROM articles WHERE DATE_FORMAT(created_at, '%Y-%m') = ?");
    $stmt->bind_param('s', $thisMonth);
    $stmt->execute();
    $summary['views_this_month'] = $stmt->get_result()->fetch_assoc()['total'] ?? 0;

    // أكثر التصنيفات نشاطاً
    $result = $conn->query("SELECT c.name, COUNT(a.id) as count FROM categories c LEFT JOIN articles a ON c.id = a.category_id GROUP BY c.id ORDER BY count DESC LIMIT 1");
    $topCategory = $result->fetch_assoc();
    $summary['top_category'] = $topCategory ? $topCategory['name'] : 'لا يوجد';

    // متوسط المشاهدات
    $result = $conn->query("SELECT AVG(views) as avg_views FROM articles WHERE views > 0");
    $summary['avg_views'] = round($result->fetch_assoc()['avg_views'] ?? 0);

    // عدد المقالات المسودة
    $result = $conn->query("SELECT COUNT(*) as count FROM articles WHERE status = 'draft'");
    $summary['draft_articles'] = $result->fetch_assoc()['count'];

    // آخر نشاط
    $result = $conn->query("SELECT title, created_at FROM articles ORDER BY created_at DESC LIMIT 1");
    $lastActivity = $result->fetch_assoc();
    $summary['last_activity'] = $lastActivity ? [
        'title' => $lastActivity['title'],
        'time' => $lastActivity['created_at']
    ] : null;

    echo json_encode(['success' => true, 'summary' => $summary]);
}

// جلب المقالات مع فلترة متقدمة وترقيم الصفحات
function getArticlesAdvanced() {
    global $conn;

    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    $status = $_GET['status'] ?? '';
    $author = $_GET['author'] ?? '';
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $views = $_GET['views'] ?? '';
    $page = max(1, (int)($_GET['page'] ?? 1));
    $perPage = max(1, min(100, (int)($_GET['per_page'] ?? 25)));
    $sortField = $_GET['sort_field'] ?? 'created_at';
    $sortDirection = $_GET['sort_direction'] ?? 'desc';

    // التحقق من صحة حقل الترتيب
    $allowedSortFields = ['title', 'category_name', 'author_name', 'status', 'views', 'created_at'];
    if (!in_array($sortField, $allowedSortFields)) {
        $sortField = 'created_at';
    }

    // التحقق من اتجاه الترتيب
    $sortDirection = strtolower($sortDirection) === 'asc' ? 'ASC' : 'DESC';

    // بناء الاستعلام الأساسي
    $baseQuery = "FROM articles a
                  LEFT JOIN categories c ON a.category_id = c.id
                  LEFT JOIN users_d u ON a.author_id = u.id
                  WHERE 1=1";

    $params = [];
    $types = '';

    // إضافة شروط البحث
    if (!empty($search)) {
        $baseQuery .= " AND (a.title LIKE ? OR a.content LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $types .= 'ss';
    }

    if (!empty($category)) {
        $baseQuery .= " AND a.category_id = ?";
        $params[] = $category;
        $types .= 'i';
    }

    if (!empty($status)) {
        $baseQuery .= " AND a.status = ?";
        $params[] = $status;
        $types .= 's';
    }

    if (!empty($author)) {
        $baseQuery .= " AND a.author_id = ?";
        $params[] = $author;
        $types .= 'i';
    }

    if (!empty($dateFrom)) {
        $baseQuery .= " AND DATE(a.created_at) >= ?";
        $params[] = $dateFrom;
        $types .= 's';
    }

    if (!empty($dateTo)) {
        $baseQuery .= " AND DATE(a.created_at) <= ?";
        $params[] = $dateTo;
        $types .= 's';
    }

    // فلترة المشاهدات
    switch ($views) {
        case 'high':
            $baseQuery .= " AND a.views > 1000";
            break;
        case 'medium':
            $baseQuery .= " AND a.views BETWEEN 500 AND 1000";
            break;
        case 'low':
            $baseQuery .= " AND a.views < 500 AND a.views > 0";
            break;
        case 'zero':
            $baseQuery .= " AND a.views = 0";
            break;
    }

    // حساب إجمالي النتائج
    $countQuery = "SELECT COUNT(*) as total " . $baseQuery;
    $countStmt = $conn->prepare($countQuery);
    if (!empty($params)) {
        $countStmt->bind_param($types, ...$params);
    }
    $countStmt->execute();
    $totalItems = $countStmt->get_result()->fetch_assoc()['total'];

    // حساب معلومات الترقيم
    $totalPages = ceil($totalItems / $perPage);
    $offset = ($page - 1) * $perPage;
    $from = $totalItems > 0 ? $offset + 1 : 0;
    $to = min($offset + $perPage, $totalItems);

    // جلب البيانات مع الترقيم
    $dataQuery = "SELECT a.*, c.name as category_name, u.name as author_name " .
                 $baseQuery .
                 " ORDER BY " . ($sortField === 'category_name' ? 'c.name' :
                                ($sortField === 'author_name' ? 'u.name' : 'a.' . $sortField)) .
                 " $sortDirection LIMIT ? OFFSET ?";

    $dataParams = $params;
    $dataParams[] = $perPage;
    $dataParams[] = $offset;
    $dataTypes = $types . 'ii';

    $dataStmt = $conn->prepare($dataQuery);
    if (!empty($dataParams)) {
        $dataStmt->bind_param($dataTypes, ...$dataParams);
    }
    $dataStmt->execute();
    $result = $dataStmt->get_result();

    $articles = [];
    while ($row = $result->fetch_assoc()) {
        $articles[] = $row;
    }

    // إحصائيات المقالات
    $statsQuery = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
                    SUM(views) as total_views
                   " . $baseQuery;

    $statsStmt = $conn->prepare($statsQuery);
    if (!empty($params)) {
        $statsStmt->bind_param($types, ...$params);
    }
    $statsStmt->execute();
    $stats = $statsStmt->get_result()->fetch_assoc();

    // معلومات الترقيم
    $pagination = [
        'current_page' => $page,
        'per_page' => $perPage,
        'total_items' => $totalItems,
        'total_pages' => $totalPages,
        'from' => $from,
        'to' => $to,
        'has_prev' => $page > 1,
        'has_next' => $page < $totalPages
    ];

    echo json_encode([
        'success' => true,
        'articles' => $articles,
        'pagination' => $pagination,
        'stats' => $stats
    ]);
}

// جلب قائمة المؤلفين
function getAuthors() {
    global $conn;

    $query = "SELECT id, name FROM users_d ORDER BY name";
    $result = $conn->query($query);

    $authors = [];
    while ($row = $result->fetch_assoc()) {
        $authors[] = $row;
    }

    echo json_encode(['success' => true, 'authors' => $authors]);
}

// جلب ملخص التحليلات
function getAnalyticsSummary() {
    global $conn;

    $summary = [];

    // إجمالي المشاهدات
    $result = $conn->query("SELECT SUM(views) as total_views FROM articles");
    $summary['total_views'] = $result->fetch_assoc()['total_views'] ?? 0;

    // إجمالي المقالات
    $result = $conn->query("SELECT COUNT(*) as total_articles FROM articles");
    $summary['total_articles'] = $result->fetch_assoc()['total_articles'] ?? 0;

    // أكثر التصنيفات نشاطاً
    $result = $conn->query("
        SELECT c.name, COUNT(a.id) as count
        FROM categories c
        LEFT JOIN articles a ON c.id = a.category_id
        GROUP BY c.id, c.name
        ORDER BY count DESC
        LIMIT 1
    ");

    if ($result->num_rows > 0) {
        $summary['top_category'] = $result->fetch_assoc();
    }

    // المقالات الحديثة (آخر 30 يوم)
    $result = $conn->query("
        SELECT COUNT(*) as recent_articles
        FROM articles
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $summary['recent_articles'] = $result->fetch_assoc()['recent_articles'] ?? 0;

    echo json_encode(['success' => true, 'summary' => $summary]);
}

// جلب المشاهدات اليومية
function getDailyViews() {
    global $conn;

    $query = "SELECT
                DATE(created_at) as date,
                SUM(views) as daily_views
              FROM articles
              WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
              GROUP BY DATE(created_at)
              ORDER BY date";

    $result = $conn->query($query);

    $labels = [];
    $data = [];

    while ($row = $result->fetch_assoc()) {
        $labels[] = date('d/m', strtotime($row['date']));
        $data[] = (int)$row['daily_views'];
    }

    // إذا لم توجد بيانات، أنشئ بيانات وهمية
    if (empty($labels)) {
        $labels = ['اليوم', 'أمس', 'قبل يومين', 'قبل 3 أيام', 'قبل 4 أيام', 'قبل 5 أيام', 'قبل 6 أيام'];
        $data = [150, 200, 180, 220, 190, 160, 210];
    }

    $chartData = [
        'labels' => $labels,
        'data' => $data
    ];

    echo json_encode(['success' => true, 'chart_data' => $chartData]);
}

// جلب جدول أفضل المقالات
function getTopArticlesTable() {
    global $conn;

    $query = "SELECT a.title, a.views, a.created_at, c.name as category_name
              FROM articles a
              LEFT JOIN categories c ON a.category_id = c.id
              WHERE a.views > 0
              ORDER BY a.views DESC
              LIMIT 10";

    $result = $conn->query($query);

    $articles = [];
    while ($row = $result->fetch_assoc()) {
        $articles[] = $row;
    }

    echo json_encode(['success' => true, 'articles' => $articles]);
}

// جلب إحصائيات التصنيفات
function getCategoriesStats() {
    global $conn;

    $query = "SELECT
                c.name,
                COUNT(a.id) as articles_count,
                COALESCE(SUM(a.views), 0) as total_views,
                COALESCE(AVG(a.views), 0) as avg_views
              FROM categories c
              LEFT JOIN articles a ON c.id = a.category_id
              GROUP BY c.id, c.name
              ORDER BY articles_count DESC";

    $result = $conn->query($query);

    $categories = [];
    while ($row = $result->fetch_assoc()) {
        $row['avg_views'] = round($row['avg_views']);
        $categories[] = $row;
    }

    echo json_encode(['success' => true, 'categories' => $categories]);
}
?>
