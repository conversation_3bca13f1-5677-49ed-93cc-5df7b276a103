<?php
// اختبار حل مشكلة التداخل بين لوحة المعلومات والتحليلات
require_once 'config/database.php';

echo "<h1>🔧 اختبار حل مشكلة التداخل</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ فشل الاتصال: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
}

echo "<h2>2. مقارنة المحتوى بين لوحة المعلومات والتحليلات</h2>";

echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0;'>";

// لوحة المعلومات
echo "<div style='background: #e7f3ff; padding: 1.5rem; border-radius: 10px;'>";
echo "<h3 style='color: #0066cc; margin-top: 0;'>📊 لوحة المعلومات (Dashboard)</h3>";
echo "<h4>الغرض:</h4>";
echo "<ul>";
echo "<li>نظرة سريعة على النظام</li>";
echo "<li>إحصائيات أساسية</li>";
echo "<li>إجراءات سريعة</li>";
echo "<li>رسوم بيانية مبسطة</li>";
echo "</ul>";

echo "<h4>المحتوى:</h4>";
echo "<ul>";
echo "<li>✅ بطاقات الإحصائيات الأساسية</li>";
echo "<li>✅ آخر المقالات والأطباء</li>";
echo "<li>✅ رسم بياني مبسط للتصنيفات (أعلى 5)</li>";
echo "<li>✅ اتجاه المشاهدات (آخر 7 أيام)</li>";
echo "<li>✅ إجراءات سريعة (إضافة مقال، طبيب، إلخ)</li>";
echo "</ul>";
echo "</div>";

// قسم التحليلات
echo "<div style='background: #f0f9ff; padding: 1.5rem; border-radius: 10px;'>";
echo "<h3 style='color: #3b82f6; margin-top: 0;'>📈 قسم التحليلات (Analytics)</h3>";
echo "<h4>الغرض:</h4>";
echo "<ul>";
echo "<li>تحليلات مفصلة ومتقدمة</li>";
echo "<li>تقارير شاملة</li>";
echo "<li>رسوم بيانية متعددة</li>";
echo "<li>جداول تفصيلية</li>";
echo "</ul>";

echo "<h4>المحتوى:</h4>";
echo "<ul>";
echo "<li>✅ ملخص تحليلي شامل</li>";
echo "<li>✅ رسوم بيانية متعددة ومتقدمة</li>";
echo "<li>✅ جداول أفضل المقالات</li>";
echo "<li>✅ إحصائيات التصنيفات المفصلة</li>";
echo "<li>✅ تصدير التقارير</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h2>3. اختبار الوظائف المنفصلة</h2>";

// اختبار وظائف لوحة المعلومات
echo "<h3>🏠 وظائف لوحة المعلومات:</h3>";

$dashboard_functions = [
    'stats' => 'الإحصائيات الأساسية',
    'recent_articles' => 'آخر المقالات',
    'recent_doctors' => 'آخر الأطباء',
    'categories_chart' => 'رسم التصنيفات المبسط'
];

foreach ($dashboard_functions as $action => $description) {
    $_GET['action'] = $action;
    
    ob_start();
    try {
        include 'php/dashboard-data.php';
        $output = ob_get_clean();
        $data = json_decode($output, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ $description: يعمل بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ $description: فشل</p>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ $description: خطأ - " . $e->getMessage() . "</p>";
    }
    
    unset($_GET['action']);
}

// اختبار وظائف التحليلات
echo "<h3>📈 وظائف التحليلات المتقدمة:</h3>";

$analytics_functions = [
    'analytics_summary' => 'ملخص التحليلات المتقدم',
    'top_articles' => 'أكثر المقالات مشاهدة',
    'content_growth' => 'نمو المحتوى',
    'daily_views' => 'المشاهدات اليومية',
    'top_articles_table' => 'جدول أفضل المقالات',
    'categories_stats' => 'إحصائيات التصنيفات المفصلة'
];

foreach ($analytics_functions as $action => $description) {
    $_GET['action'] = $action;
    
    ob_start();
    try {
        include 'php/dashboard-data.php';
        $output = ob_get_clean();
        $data = json_decode($output, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ $description: يعمل بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ $description: فشل</p>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ $description: خطأ - " . $e->getMessage() . "</p>";
    }
    
    unset($_GET['action']);
}

echo "<h2>4. اختبار التصميم المنفصل</h2>";

echo "<div style='background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin: 1rem 0;'>";
echo "<h3>🎨 الفروق في التصميم:</h3>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;'>";

echo "<div>";
echo "<h4>لوحة المعلومات:</h4>";
echo "<ul>";
echo "<li>🟡 لون ذهبي للحدود والعناصر</li>";
echo "<li>📊 رسوم بيانية صغيرة ومبسطة</li>";
echo "<li>🎯 بطاقات إجراءات سريعة</li>";
echo "<li>📱 تصميم مضغوط ومركز</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>قسم التحليلات:</h4>";
echo "<ul>";
echo "<li>🔵 لون أزرق للحدود والعناصر</li>";
echo "<li>📈 رسوم بيانية كبيرة ومفصلة</li>";
echo "<li>📋 جداول تفصيلية</li>";
echo "<li>🖥️ تصميم واسع ومفصل</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<h2>5. اختبار الملفات المحدثة</h2>";

$files_to_check = [
    'admin-dashboard.php' => 'الصفحة الرئيسية',
    'js/admin-dashboard.js' => 'ملف JavaScript',
    'style/admin-dashboard.css' => 'ملف التصميم',
    'php/dashboard-data.php' => 'ملف البيانات'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        $size = round(filesize($file) / 1024, 2);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "<p style='color: green;'>✅ $description ($file): ${size}KB - آخر تعديل: $modified</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file): غير موجود</p>";
    }
}

echo "<h2>6. اختبار العناصر في الصفحة</h2>";

// فحص العناصر المطلوبة
$dashboard_elements = [
    'dashboardCategoriesChart' => 'رسم التصنيفات في لوحة المعلومات',
    'dashboardViewsChart' => 'رسم المشاهدات في لوحة المعلومات'
];

$analytics_elements = [
    'topArticlesChart' => 'رسم أكثر المقالات مشاهدة',
    'contentGrowthChart' => 'رسم نمو المحتوى',
    'categoriesDistributionChart' => 'رسم توزيع التصنيفات',
    'dailyViewsChart' => 'رسم المشاهدات اليومية'
];

echo "<h3>🏠 عناصر لوحة المعلومات:</h3>";
foreach ($dashboard_elements as $id => $description) {
    echo "<p style='color: blue;'>🔍 $description (ID: $id)</p>";
}

echo "<h3>📈 عناصر قسم التحليلات:</h3>";
foreach ($analytics_elements as $id => $description) {
    echo "<p style='color: purple;'>🔍 $description (ID: $id)</p>";
}

echo "<h2>7. النتيجة النهائية</h2>";

echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🎉 تم حل مشكلة التداخل بنجاح!</h3>";
echo "<p><strong>التحسينات المطبقة:</strong></p>";
echo "<ul>";
echo "<li>✅ فصل محتوى لوحة المعلومات عن التحليلات</li>";
echo "<li>✅ رسوم بيانية مبسطة في لوحة المعلومات</li>";
echo "<li>✅ رسوم بيانية مفصلة في قسم التحليلات</li>";
echo "<li>✅ إجراءات سريعة في لوحة المعلومات</li>";
echo "<li>✅ تقارير مفصلة في قسم التحليلات</li>";
echo "<li>✅ تصميم مميز لكل قسم</li>";
echo "</ul>";
echo "</div>";

echo "<h2>8. روابط الاختبار</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px;'>";
echo "<p><a href='admin-dashboard.php' style='color: #d4af37; font-weight: bold;'>🏠 لوحة المعلومات الرئيسية</a></p>";
echo "<p><a href='admin-dashboard.php#analytics' style='color: #3b82f6; font-weight: bold;'>📈 قسم التحليلات المفصل</a></p>";
echo "<p><a href='test-complete-dashboard.php' style='color: #28a745; font-weight: bold;'>🧪 اختبار شامل</a></p>";
echo "</div>";

echo "<h2>9. تعليمات الاستخدام</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7;'>";
echo "<h4>📋 كيفية الاستخدام:</h4>";
echo "<ol>";
echo "<li><strong>لوحة المعلومات:</strong> للحصول على نظرة سريعة والإجراءات الأساسية</li>";
echo "<li><strong>قسم التحليلات:</strong> للحصول على تحليلات مفصلة وتقارير شاملة</li>";
echo "<li><strong>التنقل:</strong> استخدم القائمة الجانبية للتنقل بين الأقسام</li>";
echo "<li><strong>الروابط السريعة:</strong> استخدم روابط 'عرض التحليل المفصل' في لوحة المعلومات</li>";
echo "</ol>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

h1 {
    color: #d4af37;
    border-bottom: 3px solid #d4af37;
    padding-bottom: 15px;
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

h2 {
    color: #2c3e50;
    margin-top: 30px;
    border-right: 4px solid #d4af37;
    padding-right: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

h3 {
    color: #34495e;
    margin-top: 20px;
    font-size: 1.1rem;
}

h4 {
    color: #2c3e50;
    margin-top: 15px;
    font-size: 1rem;
}

p {
    margin: 8px 0;
    line-height: 1.6;
}

ul, ol {
    line-height: 1.8;
}

a {
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
