<?php
// اختبار نهائي شامل للوحة التحكم بعد الإصلاحات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار نهائي للوحة التحكم</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }";
echo ".btn-primary { background: #007bff; color: white; }";
echo ".btn-success { background: #28a745; color: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🎯 اختبار نهائي شامل للوحة التحكم</h1>";

$total_tests = 0;
$passed_tests = 0;
$failed_tests = 0;

// 1. اختبار الاتصال بقاعدة البيانات
echo "<div class='section'>";
echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
$total_tests++;
if ($conn->connect_error) {
    echo "<div class='error'>❌ فشل الاتصال: " . $conn->connect_error . "</div>";
    $failed_tests++;
    exit();
} else {
    echo "<div class='success'>✅ الاتصال بقاعدة البيانات يعمل بنجاح</div>";
    $passed_tests++;
}
echo "</div>";

// 2. اختبار وجود الجداول المطلوبة
echo "<div class='section'>";
echo "<h2>2. اختبار وجود الجداول المطلوبة</h2>";

$required_tables = ['users_d', 'articles', 'categories', 'doctors', 'services'];
foreach ($required_tables as $table) {
    $total_tests++;
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<div class='success'>✅ جدول '$table' موجود</div>";
        $passed_tests++;
    } else {
        echo "<div class='error'>❌ جدول '$table' غير موجود</div>";
        $failed_tests++;
    }
}
echo "</div>";

// 3. اختبار البيانات والعلاقات
echo "<div class='section'>";
echo "<h2>3. اختبار البيانات والعلاقات</h2>";

// اختبار المستخدمين
$total_tests++;
$users_count = $conn->query("SELECT COUNT(*) as count FROM users_d")->fetch_assoc()['count'];
if ($users_count > 0) {
    echo "<div class='success'>✅ يوجد $users_count مستخدم في النظام</div>";
    $passed_tests++;
} else {
    echo "<div class='error'>❌ لا يوجد مستخدمون في النظام</div>";
    $failed_tests++;
}

// اختبار التصنيفات
$total_tests++;
$categories_count = $conn->query("SELECT COUNT(*) as count FROM categories")->fetch_assoc()['count'];
if ($categories_count > 0) {
    echo "<div class='success'>✅ يوجد $categories_count تصنيف</div>";
    $passed_tests++;
} else {
    echo "<div class='error'>❌ لا توجد تصنيفات</div>";
    $failed_tests++;
}

// اختبار المقالات
$total_tests++;
$articles_count = $conn->query("SELECT COUNT(*) as count FROM articles")->fetch_assoc()['count'];
if ($articles_count > 0) {
    echo "<div class='success'>✅ يوجد $articles_count مقال</div>";
    $passed_tests++;
} else {
    echo "<div class='warning'>⚠️ لا توجد مقالات</div>";
    $failed_tests++;
}

// اختبار علاقة المقالات بالمستخدمين
$total_tests++;
try {
    $result = $conn->query("
        SELECT COUNT(*) as count 
        FROM articles a 
        LEFT JOIN users_d u ON a.author_id = u.id 
        WHERE u.id IS NULL
    ");
    $orphaned_articles = $result->fetch_assoc()['count'];
    
    if ($orphaned_articles == 0) {
        echo "<div class='success'>✅ جميع المقالات مرتبطة بمؤلفين صحيحين</div>";
        $passed_tests++;
    } else {
        echo "<div class='error'>❌ يوجد $orphaned_articles مقال بدون مؤلف صحيح</div>";
        $failed_tests++;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص علاقة المقالات بالمؤلفين: " . $e->getMessage() . "</div>";
    $failed_tests++;
}

// اختبار علاقة المقالات بالتصنيفات
$total_tests++;
try {
    $result = $conn->query("
        SELECT COUNT(*) as count 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        WHERE c.id IS NULL
    ");
    $orphaned_categories = $result->fetch_assoc()['count'];
    
    if ($orphaned_categories == 0) {
        echo "<div class='success'>✅ جميع المقالات مرتبطة بتصنيفات صحيحة</div>";
        $passed_tests++;
    } else {
        echo "<div class='error'>❌ يوجد $orphaned_categories مقال بدون تصنيف صحيح</div>";
        $failed_tests++;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص علاقة المقالات بالتصنيفات: " . $e->getMessage() . "</div>";
    $failed_tests++;
}
echo "</div>";

// 4. اختبار ملفات PHP المطلوبة
echo "<div class='section'>";
echo "<h2>4. اختبار ملفات PHP المطلوبة</h2>";

$required_files = [
    'admin-dashboard.php' => 'لوحة التحكم الرئيسية',
    'php/dashboard-data.php' => 'ملف بيانات لوحة التحكم',
    'php/dashboard-actions.php' => 'ملف إجراءات لوحة التحكم',
    'js/admin-dashboard.js' => 'ملف JavaScript للوحة التحكم',
    'config/database.php' => 'ملف إعدادات قاعدة البيانات'
];

foreach ($required_files as $file => $description) {
    $total_tests++;
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description ($file)</div>";
        $passed_tests++;
    } else {
        echo "<div class='error'>❌ $description ($file) - غير موجود</div>";
        $failed_tests++;
    }
}
echo "</div>";

// 5. اختبار وظائف لوحة التحكم
echo "<div class='section'>";
echo "<h2>5. اختبار وظائف لوحة التحكم</h2>";

// اختبار جلب الإحصائيات
$total_tests++;
try {
    $stats = [];
    $stats['articles'] = $conn->query("SELECT COUNT(*) as total FROM articles")->fetch_assoc()['total'];
    $stats['categories'] = $conn->query("SELECT COUNT(*) as total FROM categories")->fetch_assoc()['total'];
    $stats['doctors'] = $conn->query("SELECT COUNT(*) as total FROM doctors")->fetch_assoc()['total'];
    $stats['services'] = $conn->query("SELECT COUNT(*) as total FROM services")->fetch_assoc()['total'];
    $stats['views'] = $conn->query("SELECT SUM(views) as total FROM articles")->fetch_assoc()['total'] ?? 0;
    
    echo "<div class='success'>✅ جلب الإحصائيات يعمل بنجاح</div>";
    echo "<div class='info'>📊 المقالات: {$stats['articles']} | التصنيفات: {$stats['categories']} | الأطباء: {$stats['doctors']} | الخدمات: {$stats['services']} | المشاهدات: {$stats['views']}</div>";
    $passed_tests++;
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "</div>";
    $failed_tests++;
}

// اختبار جلب المقالات الحديثة
$total_tests++;
try {
    $result = $conn->query("
        SELECT a.id, a.title, a.created_at, c.name as category_name 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        ORDER BY a.created_at DESC 
        LIMIT 3
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>✅ جلب المقالات الحديثة يعمل بنجاح</div>";
        $passed_tests++;
    } else {
        echo "<div class='warning'>⚠️ لا توجد مقالات حديثة</div>";
        $passed_tests++; // ليس خطأ، فقط لا توجد بيانات
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في جلب المقالات الحديثة: " . $e->getMessage() . "</div>";
    $failed_tests++;
}
echo "</div>";

// 6. النتيجة النهائية
echo "<div class='section'>";
echo "<h2>6. النتيجة النهائية</h2>";

$success_rate = round(($passed_tests / $total_tests) * 100, 1);

echo "<div class='info'>";
echo "<h3>📈 تقرير الاختبار:</h3>";
echo "<p><strong>إجمالي الاختبارات:</strong> $total_tests</p>";
echo "<p><strong>الاختبارات الناجحة:</strong> $passed_tests</p>";
echo "<p><strong>الاختبارات الفاشلة:</strong> $failed_tests</p>";
echo "<p><strong>معدل النجاح:</strong> $success_rate%</p>";
echo "</div>";

if ($success_rate >= 90) {
    echo "<div class='success'>";
    echo "<h3>🎉 ممتاز! النظام يعمل بشكل مثالي</h3>";
    echo "<p>لوحة التحكم جاهزة للاستخدام بدون مشاكل.</p>";
    echo "</div>";
} elseif ($success_rate >= 75) {
    echo "<div class='warning'>";
    echo "<h3>⚠️ جيد مع بعض التحذيرات</h3>";
    echo "<p>النظام يعمل بشكل جيد ولكن هناك بعض النقاط التي تحتاج تحسين.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ يحتاج إصلاحات إضافية</h3>";
    echo "<p>هناك مشاكل تحتاج إلى إصلاح قبل استخدام النظام.</p>";
    echo "</div>";
}

echo "<div style='margin-top: 20px;'>";
echo "<a href='admin-dashboard.php' class='btn btn-primary'>🚀 انتقل إلى لوحة التحكم</a>";
echo "<a href='fix-dashboard-conflicts.php' class='btn btn-success'>🔧 تشغيل الإصلاحات</a>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

$conn->close();
?>
