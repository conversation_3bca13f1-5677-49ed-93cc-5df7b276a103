<?php
// إصلاح شامل لمشاكل التضارب في لوحة التحكم
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشاكل لوحة التحكم</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }";
echo ".btn-primary { background: #007bff; color: white; }";
echo ".btn-success { background: #28a745; color: white; }";
echo ".btn-danger { background: #dc3545; color: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح شامل لمشاكل لوحة التحكم</h1>";

$errors_fixed = 0;
$warnings_found = 0;

// 1. فحص الاتصال بقاعدة البيانات
echo "<div class='section'>";
echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
if ($conn->connect_error) {
    echo "<div class='error'>❌ فشل الاتصال: " . $conn->connect_error . "</div>";
    exit();
} else {
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
}
echo "</div>";

// 2. إصلاح مراجع الجداول الخاطئة
echo "<div class='section'>";
echo "<h2>2. إصلاح مراجع الجداول الخاطئة</h2>";

// فحص وجود جدول users_d
$result = $conn->query("SHOW TABLES LIKE 'users_d'");
if ($result->num_rows == 0) {
    echo "<div class='warning'>⚠️ جدول users_d غير موجود، سأقوم بإنشاؤه...</div>";
    
    $create_users_d = "CREATE TABLE `users_d` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($create_users_d)) {
        echo "<div class='success'>✅ تم إنشاء جدول users_d</div>";
        
        // إضافة بيانات افتراضية
        $default_users = [
            ['admin', '<EMAIL>', 'admin123'],
            ['doctor1', '<EMAIL>', 'doctor123']
        ];
        
        foreach ($default_users as $user) {
            $stmt = $conn->prepare("INSERT IGNORE INTO users_d (name, email, password) VALUES (?, ?, ?)");
            $stmt->bind_param("sss", $user[0], $user[1], $user[2]);
            $stmt->execute();
        }
        echo "<div class='success'>✅ تم إضافة المستخدمين الافتراضيين</div>";
        $errors_fixed++;
    } else {
        echo "<div class='error'>❌ فشل في إنشاء جدول users_d: " . $conn->error . "</div>";
    }
} else {
    echo "<div class='success'>✅ جدول users_d موجود</div>";
}

// فحص وجود الجداول الأخرى
$required_tables = ['categories', 'articles', 'doctors', 'services'];
foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows == 0) {
        echo "<div class='warning'>⚠️ جدول $table غير موجود</div>";
        $warnings_found++;
    } else {
        echo "<div class='success'>✅ جدول $table موجود</div>";
    }
}
echo "</div>";

// 3. إصلاح العلاقات الخارجية
echo "<div class='section'>";
echo "<h2>3. إصلاح العلاقات الخارجية</h2>";

// فحص المقالات بدون مؤلف صحيح
try {
    $orphaned_articles = $conn->query("
        SELECT a.id, a.title, a.author_id 
        FROM articles a 
        LEFT JOIN users_d u ON a.author_id = u.id 
        WHERE u.id IS NULL
    ");
    
    if ($orphaned_articles && $orphaned_articles->num_rows > 0) {
        echo "<div class='warning'>⚠️ يوجد " . $orphaned_articles->num_rows . " مقال بدون مؤلف صحيح</div>";
        
        // إصلاح المقالات اليتيمة بتعيين المؤلف الافتراضي
        $default_author = $conn->query("SELECT id FROM users_d LIMIT 1");
        if ($default_author && $default_author->num_rows > 0) {
            $author_id = $default_author->fetch_assoc()['id'];
            
            $fix_query = "UPDATE articles SET author_id = ? WHERE author_id NOT IN (SELECT id FROM users_d)";
            $stmt = $conn->prepare($fix_query);
            $stmt->bind_param('i', $author_id);
            
            if ($stmt->execute()) {
                echo "<div class='success'>✅ تم إصلاح المقالات اليتيمة بتعيين المؤلف الافتراضي</div>";
                $errors_fixed++;
            }
        }
    } else {
        echo "<div class='success'>✅ جميع المقالات لها مؤلفون صحيحون</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص المقالات: " . $e->getMessage() . "</div>";
}

// فحص المقالات بدون تصنيف صحيح
try {
    $orphaned_categories = $conn->query("
        SELECT a.id, a.title, a.category_id 
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        WHERE c.id IS NULL
    ");
    
    if ($orphaned_categories && $orphaned_categories->num_rows > 0) {
        echo "<div class='warning'>⚠️ يوجد " . $orphaned_categories->num_rows . " مقال بدون تصنيف صحيح</div>";
        
        // إنشاء تصنيف افتراضي إذا لم يوجد
        $default_category = $conn->query("SELECT id FROM categories LIMIT 1");
        if ($default_category && $default_category->num_rows > 0) {
            $category_id = $default_category->fetch_assoc()['id'];
        } else {
            // إنشاء تصنيف افتراضي
            $conn->query("INSERT INTO categories (name, description) VALUES ('عام', 'تصنيف عام للمقالات')");
            $category_id = $conn->insert_id;
            echo "<div class='info'>📝 تم إنشاء تصنيف افتراضي</div>";
        }
        
        $fix_query = "UPDATE articles SET category_id = ? WHERE category_id NOT IN (SELECT id FROM categories)";
        $stmt = $conn->prepare($fix_query);
        $stmt->bind_param('i', $category_id);
        
        if ($stmt->execute()) {
            echo "<div class='success'>✅ تم إصلاح المقالات بدون تصنيف</div>";
            $errors_fixed++;
        }
    } else {
        echo "<div class='success'>✅ جميع المقالات لها تصنيفات صحيحة</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص التصنيفات: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 4. إضافة بيانات تجريبية إذا كانت الجداول فارغة
echo "<div class='section'>";
echo "<h2>4. إضافة بيانات تجريبية</h2>";

// فحص وإضافة تصنيفات
$categories_count = $conn->query("SELECT COUNT(*) as count FROM categories")->fetch_assoc()['count'];
if ($categories_count == 0) {
    echo "<div class='info'>📝 إضافة تصنيفات افتراضية...</div>";
    
    $default_categories = [
        ['العناية بالأسنان', 'نصائح وإرشادات للعناية اليومية بالأسنان'],
        ['تقويم الأسنان', 'معلومات حول تقويم الأسنان'],
        ['جراحة الأسنان', 'معلومات حول العمليات الجراحية'],
        ['طب أسنان الأطفال', 'العناية بأسنان الأطفال'],
        ['زراعة الأسنان', 'معلومات حول زراعة الأسنان'],
        ['تجميل الأسنان', 'علاجات تجميل الأسنان']
    ];
    
    foreach ($default_categories as $category) {
        $stmt = $conn->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        $stmt->bind_param("ss", $category[0], $category[1]);
        $stmt->execute();
    }
    echo "<div class='success'>✅ تم إضافة " . count($default_categories) . " تصنيف</div>";
    $errors_fixed++;
} else {
    echo "<div class='success'>✅ يوجد $categories_count تصنيف</div>";
}

// فحص وإضافة مقالات
$articles_count = $conn->query("SELECT COUNT(*) as count FROM articles")->fetch_assoc()['count'];
if ($articles_count == 0) {
    echo "<div class='info'>📝 إضافة مقالات تجريبية...</div>";
    
    $author_id = $conn->query("SELECT id FROM users_d LIMIT 1")->fetch_assoc()['id'];
    $category_id = $conn->query("SELECT id FROM categories LIMIT 1")->fetch_assoc()['id'];
    
    $default_articles = [
        ['أهمية تنظيف الأسنان اليومي', 'تنظيف الأسنان يومياً أمر ضروري للحفاظ على صحة الفم والأسنان...', 1250],
        ['تقويم الأسنان للكبار', 'تقويم الأسنان ليس مقتصراً على الأطفال فقط...', 890],
        ['زراعة الأسنان: الحل الأمثل', 'زراعة الأسنان تعتبر الحل الأمثل لتعويض الأسنان المفقودة...', 2100]
    ];
    
    foreach ($default_articles as $article) {
        $stmt = $conn->prepare("INSERT INTO articles (title, content, category_id, author_id, status, views) VALUES (?, ?, ?, ?, 'published', ?)");
        $stmt->bind_param("ssiii", $article[0], $article[1], $category_id, $author_id, $article[2]);
        $stmt->execute();
    }
    echo "<div class='success'>✅ تم إضافة " . count($default_articles) . " مقال</div>";
    $errors_fixed++;
} else {
    echo "<div class='success'>✅ يوجد $articles_count مقال</div>";
}
echo "</div>";

// 5. ملخص الإصلاحات
echo "<div class='section'>";
echo "<h2>5. ملخص الإصلاحات</h2>";
echo "<div class='success'>✅ تم إصلاح $errors_fixed مشكلة</div>";
echo "<div class='warning'>⚠️ تم العثور على $warnings_found تحذير</div>";

if ($errors_fixed > 0) {
    echo "<div class='info'>🔄 يُنصح بإعادة تحميل لوحة التحكم لرؤية التحسينات</div>";
    echo "<a href='admin-dashboard.php' class='btn btn-primary'>🚀 انتقل إلى لوحة التحكم</a>";
}

echo "<a href='check-dashboard-conflicts.php' class='btn btn-success'>🔍 فحص النظام مرة أخرى</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

$conn->close();
?>
