/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gold: #D4AF37;
    --secondary-gold: #F4E4BC;
    --dark-gold: #B8860B;
    --light-gold: #FFF8DC;
    --white: #FFFFFF;
    --dark-text: #2C2C2C;
    --light-text: #666666;
    --accent-blue: #1E3A8A;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 25px rgba(212, 175, 55, 0.15);
    border-bottom: 1px solid var(--secondary-gold);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-blue);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.logo i {
    margin-left: 10px;
    font-size: 2rem;
    color: var(--primary-gold);
    filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-text);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: var(--primary-gold);
    text-shadow: 0 1px 2px rgba(212, 175, 55, 0.3);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    transition: width 0.3s ease;
    border-radius: 1px;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 50%, var(--dark-gold) 100%);
    color: white;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: calc(100vh - 80px);
    position: relative;
    z-index: 2;
}

.hero-content {
    animation: fadeInUp 1s ease;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 1s ease 0.1s both;
}

.hero-badge i {
    color: var(--secondary-gold);
    font-size: 1rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    animation: fadeInUp 1s ease 0.2s both;
}

.title-highlight {
    background: linear-gradient(135deg, var(--secondary-gold), var(--primary-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
    animation: fadeInUp 1s ease 0.3s both;
}

.hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease 0.4s both;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.feature-item i {
    color: var(--secondary-gold);
    font-size: 1.1rem;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease 0.5s both;
}

.btn {
    padding: 16px 32px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
}

.btn i {
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--secondary-gold);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.1);
}

.hero-stats {
    display: flex;
    gap: 3rem;
    animation: fadeInUp 1s ease 0.6s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--secondary-gold);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Hero Image Section */
.hero-image {
    position: relative;
    animation: fadeInUp 1s ease 0.3s both;
}

.image-container {
    position: relative;
    z-index: 2;
}

.hero-main-image {
    width: 100%;
    height: auto;
    max-width: 500px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    object-fit: cover;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    min-height: 400px;
}

.hero-main-image:hover {
    transform: scale(1.02);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--accent-blue);
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    color: var(--primary-gold);
    font-size: 1.2rem;
}

.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    bottom: 30%;
    left: -15%;
    animation-delay: 1s;
}

.card-3 {
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.hero-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    right: 15%;
    animation-delay: 2s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    top: 70%;
    left: 20%;
    animation-delay: 4s;
}

/* Hero Wave */
.hero-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
    transform: rotate(180deg);
}

.hero-wave svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 60px;
}

.hero-wave .shape-fill {
    fill: #f8f9fa;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 2px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, var(--light-gold) 100%);
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-gold), transparent);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(212, 175, 55, 0.1);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 15px 15px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
    border-color: var(--secondary-gold);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
    position: relative;
}

.service-icon::before {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    z-index: -1;
}

.service-icon i {
    font-size: 2rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.service-card h3 {
    font-size: 1.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.service-card p {
    color: var(--light-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-details {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.service-price,
.service-duration,
.service-category {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.service-price i {
    color: #28a745;
}

.service-duration i {
    color: #17a2b8;
}

.service-category i {
    color: #d4af37;
}

.service-price span {
    font-weight: 600;
    color: #28a745;
}

.no-services {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-services i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d4af37;
}

/* About Section */
.about {
    padding: 6rem 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
    position: relative;
}

.about-text h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 2px;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--light-text);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature i {
    color: var(--primary-gold);
    font-size: 1.2rem;
    filter: drop-shadow(0 1px 2px rgba(212, 175, 55, 0.3));
}

.about-cta {
    margin-top: 2rem;
}

.about-cta .btn {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.about-cta .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.about-image {
    position: relative;
}

.about-bg {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    position: relative;
}

.about-bg::after {
    content: '🦷';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8rem;
    opacity: 0.3;
}

/* Team Section */
.team {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--light-gold) 0%, #f8f9fa 100%);
    position: relative;
}

.team::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-gold), transparent);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(212, 175, 55, 0.1);
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 15px 15px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
    border-color: var(--secondary-gold);
}

.team-card:hover::before {
    opacity: 1;
}

.team-image {
    margin-bottom: 1.5rem;
}

.doctor-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 2.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.team-card h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.specialty {
    color: #d4af37;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.experience {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #6c757d;
    margin-bottom: 1rem;
    font-weight: 500;
}

.experience i {
    color: #d4af37;
}

.bio {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.doctor-contact {
    margin-top: 1rem;
}

.contact-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: #d4af37;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.contact-btn:hover {
    background: #b8860b;
    transform: translateY(-2px);
    color: white;
}

.no-doctors {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-doctors i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d4af37;
}

.doctor-placeholder {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
    position: relative;
}

.doctor-placeholder::before {
    content: '';
    position: absolute;
    inset: 3px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-blue));
    z-index: -1;
}

.doctor-placeholder i {
    font-size: 3rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.team-card h3 {
    font-size: 1.5rem;
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.specialty {
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 1rem;
    text-shadow: 0 1px 2px rgba(212, 175, 55, 0.2);
}

/* Contact Section */
.contact {
    padding: 6rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item i {
    color: var(--primary-gold);
    font-size: 1.5rem;
    margin-top: 0.5rem;
    filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.contact-item h4 {
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    color: white;
    padding: 3rem 0 1rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-gold), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--secondary-gold);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--secondary-gold);
    text-shadow: 0 1px 2px rgba(244, 228, 188, 0.3);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--secondary-gold), var(--primary-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(244, 228, 188, 0.3);
}

.social-links a i {
    color: var(--accent-blue);
    font-weight: 600;
}

.social-links a:hover {
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #444;
    color: #ccc;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        right: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        right: 0;
    }

    /* Hero Responsive */
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-features {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
        gap: 2rem;
    }

    .hero-main-image {
        max-width: 350px;
    }

    .floating-card {
        display: none;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Mobile Login Button */
    .login-btn {
        width: 45px;
        height: 45px;
    }

    .login-btn i {
        font-size: 1.1rem;
    }

    /* تحسين التصميم المتجاوب للهاتف */
    .nav-actions {
        margin-left: 10px;
    }
}




/* Login Button Styles */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.login-btn {
    position: relative;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    text-decoration: none;
}

.login-btn::before {
    content: '';
    position: absolute;
    inset: 2px;
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.login-btn:hover::before {
    opacity: 1;
}

.login-btn i {
    font-size: 1.2rem;
    color: white;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.login-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.login-btn:hover i {
    transform: scale(1.1);
    color: #fff;
}

.login-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

/* Login Modal Styles */
.login-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.login-modal.active {
    display: flex;
    opacity: 1;
}

.login-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
}

.login-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Login Success Message */
.login-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.3);
    z-index: 10002;
    display: none;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
    transition: all 0.3s ease;
}

.login-success.active {
    display: block;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.success-content i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #4CAF50;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.success-content h3 {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.success-content p {
    opacity: 0.9;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes loginPulse {
    0% {
        box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(212, 175, 55, 0.5);
    }
    100% {
        box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    }
}

/* تأثير النقر على أيقونة تسجيل الدخول */
.login-btn:active {
    animation: loginPulse 0.3s ease;
}

/* تأثير التحميل */
.login-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.login-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Additional Responsive Styles for Small Screens */
@media (max-width: 480px) {
    .hero {
        padding-top: 70px;
    }

    .hero-container {
        min-height: calc(100vh - 70px);
        padding: 0 10px;
    }

    .hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 0.8rem;
        margin-bottom: 2rem;
    }

    .feature-item {
        font-size: 0.8rem;
    }

    .btn {
        padding: 14px 24px;
        font-size: 0.9rem;
    }

    .hero-stats {
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .hero-main-image {
        max-width: 280px;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .hero-container {
        gap: 3rem;
    }

    .hero-title {
        font-size: 3.2rem;
    }

    .hero-features {
        gap: 1.5rem;
    }

    .hero-stats {
        gap: 2.5rem;
    }

    .floating-card {
        padding: 12px 16px;
        font-size: 0.9rem;
    }
}

/* تحسينات إضافية للتأثيرات */
@keyframes floatEnhanced {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(1deg);
    }
    66% {
        transform: translateY(-20px) rotate(-1deg);
    }
}

@keyframes floatSlow {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-15px) scale(1.05);
    }
}

/* تطبيق التأثيرات المحسنة */
.floating-card:nth-child(2) {
    animation: floatSlow 4s ease-in-out infinite;
}

.floating-card:nth-child(3) {
    animation: floatEnhanced 5s ease-in-out infinite;
}

/* تأثير التدرج للنص المميز */
.title-highlight {
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, var(--secondary-gold), var(--primary-gold));
        -webkit-background-clip: text;
        background-clip: text;
    }
    50% {
        background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
        -webkit-background-clip: text;
        background-clip: text;
    }
}

/* تحسين تأثير الموجة */
.hero-wave svg {
    animation: waveMove 10s ease-in-out infinite;
}

@keyframes waveMove {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-20px);
    }
}



