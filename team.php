<?php
// جلب جميع بيانات الأطباء من قاعدة البيانات
require_once 'config/database.php';

// جلب جميع الأطباء
$doctors_query = "SELECT * FROM doctors ORDER BY created_at DESC";
$doctors_result = $conn->query($doctors_query);
$doctors = [];
while ($row = $doctors_result->fetch_assoc()) {
    $doctors[] = $row;
}

// جلب إحصائيات الأطباء
$stats_query = "SELECT 
    COUNT(*) as total_doctors,
    AVG(experience) as avg_experience,
    COUNT(DISTINCT specialty) as specialties_count
    FROM doctors";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فريقنا الطبي - سمايل ديزاين لطب الأسنان</title>
    <link rel="stylesheet" href="style/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .team-hero {
            background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
            color: white;
            padding: 6rem 0 4rem;
            text-align: center;
        }
        
        .team-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .team-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .team-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
            padding: 0 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .team-content {
            padding: 4rem 0;
            background: #f8f9fa;
        }
        
        .team-filters {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 0.8rem 1.5rem;
            border: 2px solid #d4af37;
            background: white;
            color: #d4af37;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: #d4af37;
            color: white;
        }
        
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .team-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .team-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #d4af37, #f4d03f);
        }
        
        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .doctor-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 3rem;
            color: white;
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }
        
        .doctor-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .doctor-specialty {
            color: #d4af37;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .doctor-experience {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #6c757d;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .doctor-bio {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }
        
        .doctor-contact {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        
        .contact-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background: #d4af37;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .contact-btn:hover {
            background: #b8860b;
            transform: translateY(-2px);
        }
        
        .no-doctors {
            grid-column: 1 / -1;
            text-align: center;
            padding: 4rem 2rem;
            color: #6c757d;
        }
        
        .no-doctors i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #d4af37;
        }
        
        .back-btn {
            position: fixed;
            top: 100px;
            right: 2rem;
            background: #d4af37;
            color: white;
            padding: 1rem;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-btn:hover {
            background: #b8860b;
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .team-hero h1 {
                font-size: 2rem;
            }
            
            .team-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .team-filters {
                padding: 0 1rem;
            }
            
            .back-btn {
                right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="index.php#team" class="back-btn" title="العودة للصفحة الرئيسية">
        <i class="fas fa-arrow-right"></i>
    </a>

    <!-- Team Hero Section -->
    <section class="team-hero">
        <div class="container">
            <h1>فريقنا الطبي المتميز</h1>
            <p>نفخر بفريق من أمهر الأطباء المتخصصين في جميع مجالات طب الأسنان، مع سنوات من الخبرة والتميز في تقديم أفضل الخدمات الطبية</p>
            
            <div class="team-stats">
                <div class="stat-card">
                    <span class="stat-number"><?php echo $stats['total_doctors']; ?></span>
                    <span class="stat-label">طبيب متخصص</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number"><?php echo round($stats['avg_experience']); ?></span>
                    <span class="stat-label">متوسط سنوات الخبرة</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number"><?php echo $stats['specialties_count']; ?></span>
                    <span class="stat-label">تخصص طبي</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Content -->
    <section class="team-content">
        <div class="container">
            <!-- Filters -->
            <div class="team-filters">
                <button class="filter-btn active" data-filter="all">جميع الأطباء</button>
                <?php
                // جلب التخصصات المختلفة
                $specialties_query = "SELECT DISTINCT specialty FROM doctors ORDER BY specialty";
                $specialties_result = $conn->query($specialties_query);
                while ($specialty = $specialties_result->fetch_assoc()):
                ?>
                <button class="filter-btn" data-filter="<?php echo htmlspecialchars($specialty['specialty']); ?>">
                    <?php echo htmlspecialchars($specialty['specialty']); ?>
                </button>
                <?php endwhile; ?>
            </div>

            <!-- Team Grid -->
            <div class="team-grid">
                <?php if (!empty($doctors)): ?>
                    <?php foreach ($doctors as $doctor): ?>
                    <div class="team-card" data-specialty="<?php echo htmlspecialchars($doctor['specialty']); ?>">
                        <div class="doctor-avatar">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h3 class="doctor-name"><?php echo htmlspecialchars($doctor['name']); ?></h3>
                        <p class="doctor-specialty"><?php echo htmlspecialchars($doctor['specialty']); ?></p>
                        <div class="doctor-experience">
                            <i class="fas fa-calendar-alt"></i>
                            <span>خبرة <?php echo $doctor['experience']; ?> سنة</span>
                        </div>
                        <?php if (!empty($doctor['bio'])): ?>
                        <p class="doctor-bio"><?php echo htmlspecialchars($doctor['bio']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($doctor['phone'])): ?>
                        <div class="doctor-contact">
                            <a href="tel:<?php echo $doctor['phone']; ?>" class="contact-btn">
                                <i class="fas fa-phone"></i>
                                اتصل الآن
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-doctors">
                        <i class="fas fa-user-md"></i>
                        <h3>لا توجد بيانات أطباء</h3>
                        <p>لا توجد بيانات أطباء متاحة حالياً. يرجى المحاولة لاحقاً.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            const teamCards = document.querySelectorAll('.team-card');

            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterBtns.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    const filter = this.getAttribute('data-filter');

                    teamCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-specialty') === filter) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeIn 0.5s ease';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Add fade in animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
